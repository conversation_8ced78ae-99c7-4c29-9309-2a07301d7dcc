import React from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Pressable,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay
} from 'react-native-reanimated';
import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows, AnimationConfig, BlurIntensity } from '../constants/Colors';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';
import LottieIcon from './LottieIcon';

interface ModernButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'glass' | 'floating';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  fullWidth?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  hapticFeedback?: boolean;
  glowEffect?: boolean;
  shadowIntensity?: 'none' | 'subtle' | 'medium' | 'strong';
  borderRadius?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  animationDuration?: number;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export const ModernButton: React.FC<ModernButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  disabled = false,
  fullWidth = false,
  loading = false,
  style,
  textStyle,
  hapticFeedback = true,
  glowEffect = false,
  shadowIntensity = 'medium',
  borderRadius = 'lg',
  animationDuration = 150,
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);
  const glowOpacity = useSharedValue(0);
  const shadowScale = useSharedValue(1);
  const rotateY = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { rotateY: `${rotateY.value}deg` },
      { scale: shadowScale.value }
    ],
    // Keep full opacity - no opacity animation
  }));

  const glowAnimatedStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
    transform: [{ scale: interpolate(glowOpacity.value, [0, 1], [0.8, 1.2]) }],
  }));

  const triggerHaptic = () => {
    if (hapticFeedback) {
      // Haptic feedback would be implemented here
      // For now, we'll use a visual feedback
    }
  };

  const handlePressIn = () => {
    'worklet';
    const scaleValue = variant === 'floating' ? 0.92 : 0.96;
    scale.value = withSpring(scaleValue, {
      damping: 20,
      stiffness: 400,
      mass: 0.8
    });

    // Remove opacity animation and enhance glow effect instead
    glowOpacity.value = withTiming(0.8, { duration: animationDuration });
    shadowScale.value = withSpring(1.08, { damping: 15, stiffness: 300 });
    runOnJS(triggerHaptic)();
  };

  const handlePressOut = () => {
    'worklet';
    scale.value = withSpring(1, {
      damping: 20,
      stiffness: 400,
      mass: 0.8
    });

    // Remove opacity animation and use glow effect
    glowOpacity.value = withTiming(0, { duration: animationDuration * 2 });
    shadowScale.value = withSpring(1, { damping: 15, stiffness: 300 });
  };

  const handlePress = () => {
    'worklet';
    // Add a subtle rotation effect for premium feel
    rotateY.value = withSequence(
      withTiming(2, { duration: 100 }),
      withTiming(-2, { duration: 100 }),
      withTiming(0, { duration: 100 })
    );

    runOnJS(onPress)();
  };

  const getButtonStyle = () => {
    const baseStyle = [
      styles.button,
      styles[`${size}Button`],
      styles[`${variant}Button`],
      fullWidth && styles.fullWidth,
      disabled && styles.disabled,
      style,
    ];
    return baseStyle;
  };

  const getTextStyle = () => {
    return [
      styles.text,
      styles[`${size}Text`],
      styles[`${variant}Text`],
      disabled && styles.disabledText,
      textStyle,
    ];
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 14;
      case 'lg': return 20;
      case 'xl': return 24;
      default: return 16;
    }
  };

  const getIconColor = () => {
    if (disabled) return Colors.mutedForeground;

    switch (variant) {
      case 'primary': return Colors.brandForeground;
      case 'secondary': return Colors.foreground;
      case 'outline': return Colors.brand;
      case 'ghost': return Colors.brand;
      case 'destructive': return Colors.destructiveForeground;
      default: return Colors.brandForeground;
    }
  };

  const renderContent = () => (
    <View style={styles.content}>
      {loading && (
        <View style={styles.loadingContainer}>
          <Text style={[getTextStyle(), { opacity: 0.6 }]}>Loading...</Text>
        </View>
      )}
      {!loading && (
        <>
          {icon && iconPosition === 'left' && (
            <Ionicons
              name={icon}
              size={getIconSize()}
              color={getIconColor()}
              style={styles.iconLeft}
            />
          )}
          <Text style={getTextStyle()} numberOfLines={1}>
            {title}
          </Text>
          {icon && iconPosition === 'right' && (
            <Ionicons
              name={icon}
              size={getIconSize()}
              color={getIconColor()}
              style={styles.iconRight}
            />
          )}
        </>
      )}
    </View>
  );

  if (variant === 'primary') {
    return (
      <View style={styles.buttonContainer}>
        {/* Glow Effect Background */}
        {glowEffect && (
          <Animated.View
            style={[
              styles.glowContainer,
              getButtonStyle(),
              glowAnimatedStyle,
              {
                position: 'absolute',
                backgroundColor: '#6B7C5A',
                shadowColor: '#6B7C5A',
                shadowOffset: { width: 0, height: 0 },
                shadowOpacity: 0.6,
                shadowRadius: 20,
                elevation: 10,
              }
            ]}
          />
        )}

        <AnimatedTouchableOpacity
          style={[getButtonStyle(), animatedStyle]}
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled || loading}
          activeOpacity={1}
        >
          <LinearGradient
            colors={disabled ? [Colors.muted, Colors.muted] : ['#6B7C5A', '#6B7C5A']}
            style={styles.gradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {renderContent()}
          </LinearGradient>
        </AnimatedTouchableOpacity>
      </View>
    );
  }

  return (
    <AnimatedTouchableOpacity
      style={[getButtonStyle(), animatedStyle]}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled || loading}
      activeOpacity={1}
    >
      {renderContent()}
    </AnimatedTouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Container for glow effect
  buttonContainer: {
    position: 'relative',
  },
  glowContainer: {
    borderRadius: BorderRadius.lg,
  },

  // Base Button Styles
  button: {
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.5,
  },

  // Size Variants - Apple-inspired
  smButton: {
    minHeight: 32,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
  },
  mdButton: {
    minHeight: 40,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
  },
  lgButton: {
    minHeight: 48,
    paddingHorizontal: Spacing.xxl,
    paddingVertical: Spacing.lg,
  },
  xlButton: {
    minHeight: 56,
    paddingHorizontal: Spacing.xxxl,
    paddingVertical: Spacing.xl,
  },

  // Variant Styles - Shadcn-inspired
  primaryButton: {
    backgroundColor: Colors.brand,
  },
  secondaryButton: {
    backgroundColor: Colors.secondary,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  outlineButton: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderWidth: 1,
    borderColor: Colors.brand,
  },
  ghostButton: {
    backgroundColor: 'transparent',
  },
  destructiveButton: {
    backgroundColor: Colors.destructive,
  },

  // Gradient Styles
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BorderRadius.lg,
  },

  // Content Container
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },

  // Loading State
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Text Styles - Apple Typography
  text: {
    textAlign: 'center',
    fontWeight: FontWeights.medium,
    letterSpacing: -0.2,
  },
  smText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
  },
  mdText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
  },
  lgText: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
  },
  xlText: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
  },

  // Text Color Variants
  primaryText: {
    color: Colors.brandForeground,
  },
  secondaryText: {
    color: Colors.secondaryForeground,
  },
  outlineText: {
    color: Colors.brand,
  },
  ghostText: {
    color: Colors.brand,
  },
  destructiveText: {
    color: Colors.destructiveForeground,
  },
  disabledText: {
    color: Colors.mutedForeground,
  },

  // Icon Styles
  iconLeft: {
    marginRight: Spacing.sm,
  },
  iconRight: {
    marginLeft: Spacing.sm,
  },
});

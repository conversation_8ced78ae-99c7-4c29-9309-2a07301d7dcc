import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
  Modal,
  TextInput,
  ImageBackground,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
// Removed BlurView import
import Animated, {
  FadeInUp,
  SlideInLeft,
  FadeInDown,
  SlideInUp,
  SlideInRight,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows, BlurIntensity } from '../constants/Colors';
import { AnimationConfig } from '../utils/AnimationUtils';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import LottieIcon from '../components/LottieIcon';
import { CircularProgress } from '../components/CircularProgress';
// Removed BlurView import
import * as Haptics from 'expo-haptics';
import ApiService from '../services/ApiService';
import { useProfile } from '../contexts/ProfileContext';
import DatabaseIntegrationService from '../services/DatabaseIntegrationService';
import NotificationService from '../services/NotificationService';

const { width, height } = Dimensions.get('window');

// Types
interface MealPlan {
  day: string;
  meals: { [key: string]: { name: string; calories: number; time: string } };
  completed: boolean;
}

interface WeekPlan {
  week: Array<{
    day: string;
    meals: { [key: string]: string };
  }>;
}

const PlanScreenModern: React.FC = () => {
  const navigation = useNavigation();
  const { profile, addRecentMeal } = useProfile();
  const dbService = DatabaseIntegrationService;
  const notificationService = NotificationService;

  // Core states
  const [weekPlan, setWeekPlan] = useState<WeekPlan | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingType, setLoadingType] = useState<string | null>(null);
  const [selectedDay, setSelectedDay] = useState<string | null>(null);
  const [showCustomization, setShowCustomization] = useState(false);
  const [viewMode, setViewMode] = useState<'week' | 'day' | 'timeline'>('week');
  const [viewingRecipe, setViewingRecipe] = useState<string | null>(null);

  // Voice recognition states
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');

  // Customization states
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [calorieTarget, setCalorieTarget] = useState(2000);
  const [proteinTarget, setProteinTarget] = useState(150);
  const [mealCount, setMealCount] = useState(3);
  const [cookingTime, setCookingTime] = useState('30');
  const [budgetLevel, setBudgetLevel] = useState('medium');
  const [cuisinePreferences, setCuisinePreferences] = useState<string[]>([]);
  const [allergens, setAllergens] = useState<string[]>([]);
  const [dietaryStyle, setDietaryStyle] = useState('balanced');
  const [activityLevel, setActivityLevel] = useState('moderate');

  // Helper function to get meal info based on meal type
  const getMealInfo = (mealType: string, index: number) => {
    const mealInfoMap: { [key: string]: { type: string; icon: string; time: string; calories: number } } = {
      breakfast: { type: 'Breakfast', icon: 'sunny', time: '8:00 AM', calories: 350 },
      snack1: { type: 'Morning Snack', icon: 'nutrition', time: '10:00 AM', calories: 150 },
      lunch: { type: 'Lunch', icon: 'restaurant', time: '12:30 PM', calories: 450 },
      snack2: { type: 'Afternoon Snack', icon: 'apple', time: '3:00 PM', calories: 150 },
      dinner: { type: 'Dinner', icon: 'moon', time: '7:00 PM', calories: 500 },
      snack3: { type: 'Evening Snack', icon: 'leaf', time: '9:00 PM', calories: 100 },
    };

    return mealInfoMap[mealType] || { type: 'Meal', icon: 'restaurant', time: '12:00 PM', calories: 300 };
  };
  const [healthGoals, setHealthGoals] = useState<string[]>([]);

  // Load active weekly plan from database on mount
  useEffect(() => {
    const loadActiveWeeklyPlan = async () => {
      try {
        const activePlan = await dbService.loadActiveWeeklyPlan();
        if (activePlan) {
          setWeekPlan(activePlan);
          console.log('✅ Loaded active weekly plan from database');
        }
      } catch (error) {
        console.error('❌ Error loading weekly plan from database:', error);
      }
    };

    loadActiveWeeklyPlan();
  }, []);

  // Animation values
  const cardScale = useSharedValue(1);
  const modalOpacity = useSharedValue(0);

  const availableTags = [
    { id: 'vegetarian', label: 'Vegetarian', icon: 'leaf' },
    { id: 'vegan', label: 'Vegan', icon: 'flower' },
    { id: 'keto', label: 'Keto', icon: 'flame' },
    { id: 'low-carb', label: 'Low Carb', icon: 'trending-down' },
    { id: 'high-protein', label: 'High Protein', icon: 'fitness' },
    { id: 'gluten-free', label: 'Gluten Free', icon: 'shield-checkmark' },
    { id: 'dairy-free', label: 'Dairy Free', icon: 'water' },
    { id: 'budget-friendly', label: 'Budget Friendly', icon: 'wallet' },
    { id: 'quick-meals', label: 'Quick Meals', icon: 'time' },
    { id: 'meal-prep', label: 'Meal Prep', icon: 'cube' },
    { id: 'anti-inflammatory', label: 'Anti-Inflammatory', icon: 'medical' },
    { id: 'heart-healthy', label: 'Heart Healthy', icon: 'heart' },
    { id: 'weight-loss', label: 'Weight Loss', icon: 'trending-down' },
    { id: 'muscle-gain', label: 'Muscle Gain', icon: 'barbell' },
    { id: 'energy-boost', label: 'Energy Boost', icon: 'flash' },
  ];

  // Generate meal plan function with customization
  const generateWeeklyPlan = async () => {
    setLoading(true);
    setLoadingType('plan');
    try {
      // Build comprehensive goal string with user preferences
      let goal = 'Create a weekly meal plan with ONLY meal names';

      if (customPrompt.trim()) {
        goal += ` focusing on: ${customPrompt.trim()}`;
      } else {
        goal += ' for optimal health and nutrition';
      }

      if (selectedTags.length > 0) {
        const tagLabels = selectedTags.map(tagId =>
          availableTags.find(tag => tag.id === tagId)?.label || tagId
        );
        goal += `. Dietary preferences: ${tagLabels.join(', ')}`;
      }

      goal += `. Requirements:
      - Target ${calorieTarget} calories per day
      - Target ${proteinTarget}g protein daily
      - Plan for ${mealCount} meals per day
      - Maximum cooking time: ${cookingTime} minutes per meal
      - Budget level: ${budgetLevel}
      - Dietary style: ${dietaryStyle}
      - Activity level: ${activityLevel}`;

      if (cuisinePreferences.length > 0) {
        goal += `\n- Preferred cuisines: ${cuisinePreferences.join(', ')}`;
      }

      if (allergens.length > 0) {
        goal += `\n- CRITICAL: MUST AVOID these allergens: ${allergens.join(', ')}`;
      }

      if (healthGoals.length > 0) {
        goal += `\n- Health goals: ${healthGoals.join(', ')}`;
      }

      goal += `\n\nCRITICAL: Return ONLY meal names (2-4 words each). NO recipes, NO ingredients, NO cooking instructions.`;

      console.log('🍽️ Generating personalized meal plan...');

      let result;
      try {
        result = await ApiService.generateMealPlan(goal, mealCount);
      } catch (apiError) {
        console.error('API Error generating meal plan:', apiError);
        Alert.alert(
          'Meal Plan Generation Failed',
          'Unable to generate meal plan. Please check your internet connection and try again.',
          [
            { text: 'OK', style: 'default' },
            { text: 'Retry', onPress: () => generateWeeklyPlan() }
          ]
        );
        return;
      }

      if (result && result.week) {
        setWeekPlan(result);

        // Sync to database
        const customizations = {
          customPrompt,
          selectedTags,
          calorieTarget,
          proteinTarget,
          mealCount,
          cookingTime,
          budgetLevel,
          cuisinePreferences,
          allergens,
          dietaryStyle,
          activityLevel,
          healthGoals
        };

        await dbService.syncWeeklyPlanToDatabase(result, customizations);

        // Schedule meal reminder notifications
        await notificationService.scheduleMealReminders(result);

        console.log('✅ Personalized meal plan generated, synced to database, and notifications scheduled');
      } else {
        throw new Error('Invalid meal plan structure received');
      }
    } catch (error) {
      console.error('Error generating meal plan:', error);
      Alert.alert(
        'Error',
        'Failed to generate meal plan. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      // Add a small delay for smooth transition
      setTimeout(() => {
        setLoading(false);
        setLoadingType(null);
      }, 500);
    }
  };

  // Handle meal press for recipe details with loading animation
  const handleMealPress = async (mealType: string, meal: any) => {
    try {
      setViewingRecipe(`${mealType}-${meal.name}`);

      // Add a small delay for loading animation
      await new Promise(resolve => setTimeout(resolve, 300));

      // Create a recipe object for the detail screen
      const mealName = meal.name || meal;
      const recipeData = {
        id: `${mealType}-${Date.now()}`,
        title: mealName,
        description: `Delicious ${mealType.toLowerCase()} recipe for ${mealName}`,
        image: `https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800&h=600&fit=crop&crop=center&q=80`,
        cookTime: meal.time || '30 min',
        servings: 4,
        difficulty: 'Medium',
        calories: meal.calories || 350,
        ingredients: [
          'Fresh ingredients for ' + mealName,
          'Seasonings and spices',
          'Cooking oil',
          'Salt and pepper to taste'
        ],
        instructions: [
          'Prepare all ingredients',
          'Cook according to recipe',
          'Season to taste',
          'Serve hot and enjoy!'
        ],
        nutrition: {
          calories: meal.calories || 350,
          protein: '25g',
          carbs: '30g',
          fat: '15g'
        },
        tags: [mealType.toLowerCase(), 'healthy', 'delicious']
      };

      console.log('Navigating to recipe:', mealName);

      // Navigate to recipe detail screen
      (navigation as any).navigate('RecipeDetail', {
        recipe: recipeData
      });

    } catch (error) {
      console.error('Error navigating to recipe:', error);
      Alert.alert('Error', 'Could not open recipe details');
    } finally {
      setViewingRecipe(null);
    }
  };

  // Mark meal as consumed and add to nutrition tracking
  const markMealAsConsumed = async (mealType: string, meal: any) => {
    try {
      const mealInfo = getMealInfo(mealType, 0);
      const mealName = meal.name || meal;

      // Add to recent meals with nutrition data
      await addRecentMeal({
        name: mealName,
        time: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        calories: meal.calories || mealInfo.calories,
        protein: 25, // Estimated protein - in real app this would come from recipe data
        carbs: 30,   // Estimated carbs
        fat: 15,     // Estimated fat
        type: mealType as 'breakfast' | 'lunch' | 'dinner' | 'snack'
      });

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      Alert.alert(
        'Meal Logged! 🍽️',
        `${mealName} has been added to your nutrition tracking.`,
        [{ text: 'Great!', style: 'default' }]
      );
    } catch (error) {
      console.error('Error marking meal as consumed:', error);
      Alert.alert('Error', 'Could not log meal to nutrition tracking');
    }
  };

  // Generate alternative meal
  const generateAlternative = async (mealType: string, currentMeal: string) => {
    setLoadingType(`${mealType}-alternative`);
    try {
      // Generate alternative meal with same preferences
      const goal = `Generate ONE alternative ${mealType} meal name similar to "${currentMeal}" with same dietary preferences: ${selectedTags.join(', ')}. Return ONLY the meal name.`;

      // This would call a specific API for single meal generation
      console.log(`🔄 Generating alternative for ${currentMeal}...`);

      // For now, just show success message
      Alert.alert('Alternative Generated', `New ${mealType} option will be available soon!`);
    } catch (error) {
      console.error('Error generating alternative:', error);
      Alert.alert('Error', 'Failed to generate alternative meal.');
    } finally {
      setLoadingType(null);
    }
  };

  // Beautiful Modern Meal Card - Clean & Simple
  const BeautifulMealCard: React.FC<{
    meal: { name: string; calories: number; time: string };
    type: string;
    icon: string;
    index: number;
  }> = ({ meal, type, icon, index }) => {
    const [mealImage, setMealImage] = useState<string | null>(null);

    // Generate real Unsplash image URL
    React.useEffect(() => {
      const generateUnsplashImage = async () => {
        try {
          const searchTerm = meal.name
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .trim()
            .replace(/\s+/g, '+');

          const unsplashUrl = `https://api.unsplash.com/search/photos?query=${searchTerm}+food&client_id=QPySZeLMRd2Rw0BKoNKpXFwrHY0aSVZMwxvTmZaIZEs&per_page=1&orientation=landscape`;

          const response = await fetch(unsplashUrl);
          const data = await response.json();

          if (data.results && data.results.length > 0) {
            const imageUrl = `${data.results[0].urls.regular}&w=600&h=300&fit=crop&crop=center`;
            setMealImage(imageUrl);
          } else {
            setMealImage(`https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=600&h=300&fit=crop&crop=center&q=80`);
          }
        } catch (error) {
          setMealImage(`https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=600&h=300&fit=crop&crop=center&q=80`);
        }
      };

      generateUnsplashImage();
    }, [meal.name]);

    return (
      <Animated.View
        entering={FadeInUp.delay(index * 100).duration(500)}
        style={styles.beautifulMealCard}
      >
        {/* Top: Beautiful Image with Overlay */}
        <TouchableOpacity
          onPress={() => handleMealPress(type, meal)}
          activeOpacity={0.95}
          style={styles.beautifulCardButton}
        >
          <View style={styles.beautifulImageSection}>
            <ImageBackground
              source={{
                uri: mealImage || 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=600&h=300&fit=crop&crop=center&q=80'
              }}
              style={styles.beautifulMealImage}
              imageStyle={styles.beautifulImageStyle}
            >
              <LinearGradient
                colors={['transparent', 'rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
                style={styles.beautifulImageOverlay}
              >
                <View style={styles.beautifulTopRow}>
                  <View style={styles.beautifulMealBadge}>
                    <LottieIcon
                      name={type === 'Breakfast' ? 'apple' : type === 'Lunch' ? 'broccoli' : type === 'Dinner' ? 'chef' : 'avocado'}
                      size={14}
                      color={Colors.brandForeground}
                      enableHaptics={false}
                    />
                    <Text style={styles.beautifulMealType}>{type}</Text>
                  </View>
                  <View style={styles.beautifulCalorieBadge}>
                    <Text style={styles.beautifulCalorieText}>{meal.calories} cal</Text>
                  </View>
                </View>

                <View style={styles.beautifulBottomRow}>
                  <Text style={styles.beautifulMealName} numberOfLines={2}>{meal.name}</Text>
                  <Text style={styles.beautifulMealTime}>{meal.time}</Text>
                </View>
              </LinearGradient>
            </ImageBackground>
          </View>

          {/* Bottom: Action Buttons */}
          <View style={styles.beautifulActionsSection}>
            <TouchableOpacity
              style={styles.beautifulPrimaryAction}
              onPress={(e) => {
                e.stopPropagation();
                handleMealPress(type, meal);
              }}
              disabled={viewingRecipe === `${type}-${meal.name}`}
            >
              {viewingRecipe === `${type}-${meal.name}` ? (
                <>
                  <ActivityIndicator size="small" color="white" />
                  <Text style={styles.beautifulActionText}>Loading...</Text>
                </>
              ) : (
                <>
                  <Ionicons name="restaurant" size={16} color="white" />
                  <Text style={styles.beautifulActionText}>View Recipe</Text>
                </>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.beautifulSecondaryAction}
              onPress={(e) => {
                e.stopPropagation();
                generateAlternative(type, meal.name);
              }}
              disabled={loadingType === `${type}-alternative`}
            >
              {loadingType === `${type}-alternative` ? (
                <ActivityIndicator size="small" color="#6B7C5A" />
              ) : (
                <>
                  <Ionicons name="refresh" size={16} color="#6B7C5A" />
                  <Text style={styles.beautifulSecondaryText}>Alternative</Text>
                </>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.beautifulConsumedAction}
              onPress={(e) => {
                e.stopPropagation();
                markMealAsConsumed(type, meal);
              }}
            >
              <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
              <Text style={styles.beautifulConsumedText}>Log Meal</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  // Beautiful Day Card with Clean Layout
  const BeautifulDayCard: React.FC<{ plan: any; delay: number }> = ({ plan, delay }) => {
    const isSelected = selectedDay === plan.day;

    return (
      <Animated.View entering={FadeInUp.delay(delay).duration(400)}>
        <View style={styles.beautifulDayCard}>
          {/* Clean Day Header */}
          <TouchableOpacity
            style={[styles.beautifulDayHeader, isSelected && styles.beautifulDayHeaderSelected]}
            onPress={() => setSelectedDay(isSelected ? null : plan.day)}
            activeOpacity={0.95}
          >
            <View style={styles.beautifulDayHeaderContent}>
              <View style={styles.beautifulDayInfo}>
                <Text style={[styles.beautifulDayName, isSelected && styles.beautifulDayNameSelected]}>
                  {plan.day}
                </Text>
                <Text style={[styles.beautifulDayMeta, isSelected && styles.beautifulDayMetaSelected]}>
                  {Object.keys(plan.meals).length} meals • 1,300 cal
                </Text>
              </View>
              <View style={[styles.beautifulDayToggle, isSelected && styles.beautifulDayToggleSelected]}>
                <Ionicons
                  name={isSelected ? "chevron-up" : "chevron-down"}
                  size={20}
                  color={isSelected ? "white" : "#6B7C5A"}
                />
              </View>
            </View>
          </TouchableOpacity>

          {/* Beautiful Meals List */}
          {isSelected && (
            <Animated.View entering={FadeInDown.duration(300)} style={styles.beautifulMealsList}>
              {Object.entries(plan.meals).map(([mealType, mealName], index) => {
                const mealInfo = getMealInfo(mealType, index);
                return (
                  <BeautifulMealCard
                    key={mealType}
                    meal={{ name: mealName as string, calories: mealInfo.calories, time: mealInfo.time }}
                    type={mealInfo.type}
                    icon={mealInfo.icon}
                    index={index}
                  />
                );
              })}
            </Animated.View>
          )}
        </View>
      </Animated.View>
    );
  };

  return (
    <ImageBackground
      source={{ uri: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=800&q=80' }}
      style={styles.backgroundImage}
      resizeMode="cover"
    >
      <View style={styles.overlay}>
        <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Modern Apple-Inspired Header - Now Scrollable */}
          <SafeAreaView style={styles.modernAppleHeader}>
            <Animated.View entering={FadeInUp.duration(800)} style={styles.appleHeaderContent}>
              <View style={styles.appleHeaderTop}>
                <View style={styles.headerTextContainer}>
                  <Text style={styles.appleTitle}>Meal Plans</Text>
                  <Text style={styles.appleSubtitle}>Personalized nutrition made simple</Text>
                </View>
                <TouchableOpacity
                  style={styles.appleHeaderButton}
                  onPress={() => setShowCustomization(true)}
                >
                  <View style={styles.appleButtonInner}>
                    <Ionicons name="options" size={20} color="#6B7C5A" />
                  </View>
                </TouchableOpacity>
              </View>

              {/* Apple-style Stats Cards */}
              <Animated.View entering={SlideInUp.delay(200).duration(600)} style={styles.appleStatsContainer}>
                <View style={styles.appleStatCard}>
                  <Text style={styles.appleStatNumber}>7</Text>
                  <Text style={styles.appleStatLabel}>Days</Text>
                </View>
                <View style={styles.appleStatCard}>
                  <Text style={styles.appleStatNumber}>21</Text>
                  <Text style={styles.appleStatLabel}>Meals</Text>
                </View>
                <View style={styles.appleStatCard}>
                  <Text style={styles.appleStatNumber}>1.8k</Text>
                  <Text style={styles.appleStatLabel}>Avg Cal</Text>
                </View>
              </Animated.View>
            </Animated.View>
          </SafeAreaView>
        {/* Action Buttons */}
        {/* Apple-Style Action Cards */}
        <Animated.View entering={SlideInUp.delay(400).duration(600)} style={styles.appleActionSection}>
          <TouchableOpacity
            style={[styles.appleCreateCard, loading && styles.appleCreateCardDisabled]}
            onPress={generateWeeklyPlan}
            disabled={loading}
          >
            <View style={styles.appleCardContent}>
              <View style={styles.appleCardIcon}>
                {loading ? (
                  <ActivityIndicator size="small" color="#6B7C5A" />
                ) : (
                  <Ionicons name="add-circle" size={24} color="#6B7C5A" />
                )}
              </View>
              <View style={styles.appleCardText}>
                <Text style={styles.appleCardTitle}>
                  {loading ? 'Generating...' : 'Create New Plan'}
                </Text>
                <Text style={styles.appleCardSubtitle}>
                  AI-powered meal planning
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.appleCustomizeCard}
            onPress={() => setShowCustomization(true)}
          >
            <View style={styles.appleCardContent}>
              <View style={styles.appleCardIcon}>
                <Ionicons name="options" size={24} color="#6B7C5A" />
              </View>
              <View style={styles.appleCardText}>
                <Text style={styles.appleCardTitle}>Customize Preferences</Text>
                <Text style={styles.appleCardSubtitle}>
                  Dietary goals & restrictions
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
            </View>
          </TouchableOpacity>
        </Animated.View>

        {/* Meal Plan Display */}
        {weekPlan ? (
          <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.modernPlanContainer}>
            <View style={styles.applePlanHeader}>
              <Text style={styles.applePlanTitle}>
                {viewMode === 'week' ? 'This Week\'s Plan' : 'Today\'s Meals'}
              </Text>
              <View style={styles.appleToggleContainer}>
                <TouchableOpacity
                  style={[styles.appleToggleButton, viewMode === 'week' && styles.appleToggleButtonActive]}
                  onPress={() => setViewMode('week')}
                  activeOpacity={0.8}
                >
                  <Ionicons name="calendar" size={14} color={viewMode === 'week' ? 'white' : '#6B7C5A'} />
                  <Text style={[styles.appleToggleText, viewMode === 'week' && styles.appleToggleTextActive]}>
                    Week
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.appleToggleButton, viewMode === 'day' && styles.appleToggleButtonActive]}
                  onPress={() => setViewMode('day')}
                  activeOpacity={0.8}
                >
                  <Ionicons name="today" size={14} color={viewMode === 'day' ? 'white' : '#6B7C5A'} />
                  <Text style={[styles.appleToggleText, viewMode === 'day' && styles.appleToggleTextActive]}>
                    Day
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {viewMode === 'week' ? (
              weekPlan.week.map((dayPlan, index) => (
                <BeautifulDayCard key={dayPlan.day} plan={dayPlan} delay={index * 80} />
              ))
            ) : (
              // Day view - Beautiful clean layout for today's meals
              <Animated.View entering={FadeInUp.delay(200).duration(500)} style={styles.beautifulDayViewContainer}>
                <View style={styles.beautifulDayViewHeader}>
                  <Text style={styles.beautifulDayViewTitle}>Today's Menu</Text>
                  <Text style={styles.beautifulDayViewSubtitle}>Fresh meals curated for you</Text>
                </View>

                <View style={styles.beautifulDayViewMeals}>
                  {weekPlan.week.slice(0, 1).map((dayPlan) => (
                    <View key={dayPlan.day} style={styles.beautifulMealsList}>
                      {Object.entries(dayPlan.meals).map(([mealType, mealName], index) => {
                        const mealInfo = getMealInfo(mealType, index);
                        return (
                          <BeautifulMealCard
                            key={mealType}
                            meal={{ name: mealName as string, calories: mealInfo.calories, time: mealInfo.time }}
                            type={mealInfo.type}
                            icon={mealInfo.icon}
                            index={index}
                          />
                        );
                      })}
                    </View>
                  ))}
                </View>
              </Animated.View>
            )}
          </Animated.View>
        ) : (
          <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.modernEmptyState}>
            <LinearGradient
              colors={['rgba(107, 124, 90, 0.1)', 'rgba(107, 124, 90, 0.05)']}
              style={styles.emptyStateGradient}
            >
              <Ionicons name="restaurant-outline" size={80} color="#6B7C5A" />
              <Text style={styles.modernEmptyTitle}>Ready to Plan Your Week?</Text>
              <Text style={styles.modernEmptySubtitle}>
                Create a personalized meal plan tailored to your goals and preferences
              </Text>
              <TouchableOpacity
                style={styles.emptyStateButton}
                onPress={generateWeeklyPlan}
              >
                <LinearGradient
                  colors={['#6B7C5A', '#8B9A7A']}
                  style={styles.emptyButtonGradient}
                >
                  {loading ? (
                    <>
                      <ActivityIndicator size="small" color="white" />
                      <Text style={styles.emptyButtonText}>Creating Plan...</Text>
                    </>
                  ) : (
                    <>
                      <Ionicons name="add" size={20} color="white" />
                      <Text style={styles.emptyButtonText}>Get Started</Text>
                    </>
                  )}
                </LinearGradient>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>
        )}
      </ScrollView>

      {/* Simple Working Customize Modal */}
      <Modal
        visible={showCustomization}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCustomization(false)}
      >
        <View style={styles.simpleModalOverlay}>
          <View style={styles.simpleModalContainer}>
            {/* Header */}
            <View style={styles.simpleModalHeader}>
              <Text style={styles.simpleModalTitle}>🎯 Customize Your Plan</Text>
              <TouchableOpacity
                style={styles.simpleModalClose}
                onPress={() => setShowCustomization(false)}
              >
                <Ionicons name="close" size={24} color="#6B7C5A" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.simpleModalContent}>
              {/* Goals */}
              <View style={styles.simpleSection}>
                <Text style={styles.simpleSectionTitle}>Your Goals</Text>
                <TextInput
                  style={styles.simpleTextInput}
                  placeholder="e.g., lose weight, build muscle, improve energy..."
                  value={customPrompt}
                  onChangeText={setCustomPrompt}
                  multiline
                />
              </View>

              {/* Nutrition */}
              <View style={styles.simpleSection}>
                <Text style={styles.simpleSectionTitle}>Nutrition Targets</Text>
                <View style={styles.simpleRow}>
                  <View style={styles.simpleInputGroup}>
                    <Text style={styles.simpleLabel}>Calories</Text>
                    <TextInput
                      style={styles.simpleNumberInput}
                      value={calorieTarget.toString()}
                      onChangeText={(text) => setCalorieTarget(parseInt(text) || 2000)}
                      keyboardType="numeric"
                    />
                  </View>
                  <View style={styles.simpleInputGroup}>
                    <Text style={styles.simpleLabel}>Protein (g)</Text>
                    <TextInput
                      style={styles.simpleNumberInput}
                      value={proteinTarget.toString()}
                      onChangeText={(text) => setProteinTarget(parseInt(text) || 150)}
                      keyboardType="numeric"
                    />
                  </View>
                </View>
              </View>

              {/* Dietary Tags */}
              <View style={styles.simpleSection}>
                <Text style={styles.simpleSectionTitle}>Dietary Preferences</Text>
                <View style={styles.simpleTagsContainer}>
                  {availableTags.slice(0, 8).map((tag) => (
                    <TouchableOpacity
                      key={tag.id}
                      style={[
                        styles.simpleTag,
                        selectedTags.includes(tag.id) && styles.simpleTagSelected
                      ]}
                      onPress={() => {
                        setSelectedTags(prev =>
                          prev.includes(tag.id)
                            ? prev.filter(t => t !== tag.id)
                            : [...prev, tag.id]
                        );
                      }}
                    >
                      <Text style={[
                        styles.simpleTagText,
                        selectedTags.includes(tag.id) && styles.simpleTagTextSelected
                      ]}>
                        {tag.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Meal Count */}
              <View style={styles.simpleSection}>
                <Text style={styles.simpleSectionTitle}>Meals per day</Text>
                <View style={styles.simpleCountContainer}>
                  {[3, 4, 5, 6].map((count) => (
                    <TouchableOpacity
                      key={count}
                      style={[
                        styles.simpleCountButton,
                        mealCount === count && styles.simpleCountButtonSelected
                      ]}
                      onPress={() => setMealCount(count)}
                    >
                      <Text style={[
                        styles.simpleCountText,
                        mealCount === count && styles.simpleCountTextSelected
                      ]}>
                        {count}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </ScrollView>

            {/* Actions */}
            <View style={styles.simpleModalActions}>
              <TouchableOpacity
                style={styles.simpleCancelButton}
                onPress={() => setShowCustomization(false)}
              >
                <Text style={styles.simpleCancelText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.simpleSaveButton}
                onPress={() => {
                  setShowCustomization(false);
                  generateWeeklyPlan();
                }}
              >
                <Text style={styles.simpleSaveText}>Generate Plan</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      </View>
    </ImageBackground>
  );
};

const styles = {
  // Background Image with Overlay
  backgroundImage: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingTop: 80, // Consistent with homepage
    paddingBottom: 120, // Proper bottom spacing after content
  },

  // Apple-Inspired Header - Now Scrollable
  modernAppleHeader: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    paddingBottom: 24,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  appleHeaderContent: {
    paddingHorizontal: 20,
    paddingTop: 8,
  },
  appleHeaderTop: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'flex-start' as const,
    marginBottom: 24,
  },
  headerTextContainer: {
    flex: 1,
  },
  appleTitle: {
    fontSize: 34,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  appleSubtitle: {
    fontSize: 17,
    color: '#86868B',
    fontWeight: '400' as const,
  },
  appleHeaderButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#F2F2F7',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  appleButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'white',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Apple Stats Cards
  appleStatsContainer: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  appleStatCard: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  appleStatNumber: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#6B7C5A',
    marginBottom: 4,
  },
  appleStatLabel: {
    fontSize: 13,
    color: '#86868B',
    fontWeight: '500' as const,
  },

  // Apple Action Section
  appleActionSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    marginHorizontal: 16,
    marginTop: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
    gap: 12,
  },
  appleCreateCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  appleCreateCardDisabled: {
    opacity: 0.6,
  },
  appleCustomizeCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  appleCardContent: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    padding: 20,
  },
  appleCardIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#F2F2F7',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginRight: 16,
  },
  appleCardText: {
    flex: 1,
  },
  appleCardTitle: {
    fontSize: 17,
    fontWeight: '600' as const,
    color: '#1D1D1F',
    marginBottom: 2,
  },
  appleCardSubtitle: {
    fontSize: 15,
    color: '#86868B',
    fontWeight: '400' as const,
  },

  // Apple Plan Section - Responsive
  applePlanHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 20,
    flexWrap: 'wrap' as const,
    gap: 12,
  },
  applePlanTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    letterSpacing: -0.5,
    flex: 1,
    minWidth: 200,
  },
  appleToggleContainer: {
    flexDirection: 'row' as const,
    backgroundColor: '#F2F2F7',
    borderRadius: 10,
    padding: 2,
    minWidth: 120,
  },
  appleToggleButton: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 8,
    gap: 4,
    flex: 1,
    justifyContent: 'center' as const,
  },
  appleToggleButtonActive: {
    backgroundColor: '#6B7C5A',
  },
  appleToggleText: {
    fontSize: 12,
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },
  appleToggleTextActive: {
    color: 'white',
  },

  // Stunning Day View Styles
  stunningDayViewContainer: {
    marginBottom: 20,
  },
  stunningDayViewHeader: {
    borderRadius: 20,
    overflow: 'hidden' as const,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  stunningDayViewHeaderGradient: {
    padding: 24,
    alignItems: 'center' as const,
  },
  stunningDayViewTitle: {
    fontSize: 28,
    fontWeight: '800' as const,
    color: 'white',
    marginBottom: 8,
    textAlign: 'center' as const,
  },
  stunningDayViewSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '400' as const,
    textAlign: 'center' as const,
  },
  stunningDayViewMeals: {
    gap: 16,
  },

  // Stunning Day Card Styles
  stunningDayCard: {
    marginBottom: 20,
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 10,
    backgroundColor: 'white',
  },
  stunningDayHeader: {
    borderRadius: 20,
    overflow: 'hidden' as const,
  },
  stunningDayHeaderSelected: {
    // No additional styles needed
  },
  stunningDayHeaderGradient: {
    padding: 20,
  },
  stunningDayHeaderContent: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  stunningDayInfo: {
    flex: 1,
  },
  stunningDayName: {
    fontSize: 24,
    fontWeight: '800' as const,
    color: '#1D1D1F',
    marginBottom: 6,
  },
  stunningDayNameSelected: {
    color: 'white',
  },
  stunningDayMeta: {
    fontSize: 16,
    color: '#86868B',
    fontWeight: '500' as const,
  },
  stunningDayMetaSelected: {
    color: 'rgba(255, 255, 255, 0.9)',
  },
  stunningDayAction: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  stunningMealsList: {
    padding: 20,
    gap: 16,
    backgroundColor: '#F8F9FA',
  },

  // Stunning Meal Card Styles - Full Width, 1 per row
  stunningMealCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    marginBottom: 16,
  },
  stunningCardButton: {
    flexDirection: 'row' as const,
    padding: 0,
  },
  stunningImageContainer: {
    width: 140,
    height: 120,
  },
  stunningMealImage: {
    width: '100%' as const,
    height: '100%' as const,
    justifyContent: 'flex-end' as const,
  },
  stunningImageStyle: {
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
  stunningImageOverlay: {
    flex: 1,
    justifyContent: 'space-between' as const,
    padding: 12,
  },
  stunningMealBadge: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start' as const,
  },
  stunningMealType: {
    fontSize: 12,
    fontWeight: '700' as const,
    color: 'white',
  },
  stunningContentContainer: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between' as const,
  },
  stunningMealInfo: {
    flex: 1,
  },
  stunningMealName: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    marginBottom: 8,
    lineHeight: 22,
  },
  stunningMealMeta: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 16,
  },
  stunningTimeContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 4,
  },
  stunningMealTime: {
    fontSize: 14,
    color: '#86868B',
    fontWeight: '500' as const,
  },
  stunningCalorieContainer: {
    flexDirection: 'row' as const,
    alignItems: 'baseline' as const,
    gap: 2,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  stunningMealCalories: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: '#6B7C5A',
  },
  stunningCalorieLabel: {
    fontSize: 12,
    color: '#6B7C5A',
    fontWeight: '500' as const,
  },
  stunningActions: {
    flexDirection: 'row' as const,
    gap: 8,
  },
  stunningPrimaryAction: {
    backgroundColor: '#6B7C5A',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 10,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6,
    flex: 1,
    justifyContent: 'center' as const,
  },
  stunningActionText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: 'white',
  },
  stunningSecondaryAction: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 10,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },

  // Removed bottom safe area - using scrollView paddingBottom instead

  // Beautiful Day View Styles - Clean & Modern
  beautifulDayViewContainer: {
    gap: 20,
  },
  beautifulDayViewHeader: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    alignItems: 'center' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  beautifulDayViewTitle: {
    fontSize: 26,
    fontWeight: '800' as const,
    color: '#1D1D1F',
    marginBottom: 6,
    textAlign: 'center' as const,
  },
  beautifulDayViewSubtitle: {
    fontSize: 16,
    color: '#86868B',
    fontWeight: '400' as const,
    textAlign: 'center' as const,
  },
  beautifulDayViewMeals: {
    gap: 16,
  },

  // Beautiful Day Card Styles - Clean & Simple
  beautifulDayCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 10,
    elevation: 5,
    overflow: 'hidden' as const,
  },
  beautifulDayHeader: {
    backgroundColor: 'white',
    padding: 20,
  },
  beautifulDayHeaderSelected: {
    backgroundColor: '#6B7C5A',
  },
  beautifulDayHeaderContent: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  beautifulDayInfo: {
    flex: 1,
  },
  beautifulDayName: {
    fontSize: 22,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    marginBottom: 4,
  },
  beautifulDayNameSelected: {
    color: 'white',
  },
  beautifulDayMeta: {
    fontSize: 15,
    color: '#86868B',
    fontWeight: '500' as const,
  },
  beautifulDayMetaSelected: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  beautifulDayToggle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  beautifulDayToggleSelected: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  beautifulMealsList: {
    padding: 16,
    gap: 16,
    backgroundColor: '#F8F9FA',
  },

  // Beautiful Meal Card Styles - Clean & Modern
  beautifulMealCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 10,
    elevation: 5,
    marginBottom: 16,
  },
  beautifulCardButton: {
    // No additional styles needed
  },
  beautifulImageSection: {
    height: 180,
    position: 'relative' as const,
  },
  beautifulMealImage: {
    width: '100%' as const,
    height: '100%' as const,
    justifyContent: 'space-between' as const,
  },
  beautifulImageStyle: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  beautifulImageOverlay: {
    flex: 1,
    justifyContent: 'space-between' as const,
    padding: 16,
  },
  beautifulTopRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'flex-start' as const,
  },
  beautifulMealBadge: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6,
    backgroundColor: Colors.brand,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
  },
  beautifulMealType: {
    fontSize: 13,
    fontWeight: '700' as const,
    color: Colors.brandForeground,
  },
  beautifulCalorieBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  beautifulCalorieText: {
    fontSize: 12,
    fontWeight: '600' as const,
    color: Colors.foreground,
  },
  beautifulBottomRow: {
    gap: 4,
  },
  beautifulMealName: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: 'white',
    lineHeight: 24,
  },
  beautifulMealTime: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500' as const,
  },
  beautifulActionsSection: {
    padding: 16,
    flexDirection: 'row' as const,
    gap: 12,
  },
  beautifulPrimaryAction: {
    backgroundColor: '#6B7C5A',
    borderRadius: 14,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
    flex: 1,
    justifyContent: 'center' as const,
  },
  beautifulActionText: {
    fontSize: 13, // Smaller text
    fontWeight: '600' as const,
    color: 'white',
  },
  beautifulSecondaryAction: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 14,
    paddingHorizontal: 16, // Slightly smaller padding
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6, // Smaller gap
    flex: 1,
    justifyContent: 'center' as const,
  },
  beautifulSecondaryText: {
    fontSize: 12, // Much smaller text for Alternative
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },
  beautifulConsumedAction: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: 14,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6,
    flex: 1,
    justifyContent: 'center' as const,
  },
  beautifulConsumedText: {
    fontSize: 12,
    fontWeight: '600' as const,
    color: '#4CAF50',
  },

  // Modern Day Card with Grid Layout
  modernDayCard: {
    marginBottom: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  dayCardHeader: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  dayCardHeaderSelected: {
    backgroundColor: '#6B7C5A',
  },
  dayHeaderContent: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  dayInfo: {
    flex: 1,
  },
  modernDayName: {
    fontSize: 22,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    marginBottom: 4,
  },
  modernDayNameSelected: {
    color: 'white',
  },
  dayMealCount: {
    fontSize: 15,
    color: '#86868B',
    fontWeight: '500' as const,
  },
  dayMealCountSelected: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  modernDayMealsGrid: {
    padding: 16,
    backgroundColor: '#F8F9FA',
  },
  mealsGridContainer: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    marginHorizontal: -6,
  },

  // Large Modern Meal Card with Real Images
  modernMealCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
    marginBottom: 16,
  },
  mealCardContent: {
    padding: 0,
  },
  mealImageContainer: {
    width: '100%' as const,
    height: 200,
    position: 'relative' as const,
  },
  mealImage: {
    width: '100%' as const,
    height: '100%' as const,
    justifyContent: 'space-between' as const,
  },
  mealImageStyle: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  mealImageOverlay: {
    flex: 1,
    justifyContent: 'space-between' as const,
    padding: 16,
  },
  imageLoadingContainer: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  mealTypeContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    alignSelf: 'flex-start' as const,
  },
  mealTypeText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: 'white',
  },
  mealCaloriesOverlay: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: 'white',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: 'flex-end' as const,
  },
  mealInfo: {
    padding: 20,
  },
  modernMealName: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: '#1D1D1F',
    marginBottom: 8,
    lineHeight: 24,
  },
  modernMealTime: {
    fontSize: 16,
    color: '#86868B',
    marginBottom: 16,
  },
  mealActions: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  primaryAction: {
    backgroundColor: '#6B7C5A',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
    flex: 1,
    justifyContent: 'center' as const,
  },
  actionText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: 'white',
  },
  secondaryAction: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
    flex: 1,
    justifyContent: 'center' as const,
  },
  secondaryActionText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },

  // Compact Meal Card Styles for Modern Grid Layout
  compactMealCard: {
    flex: 1,
    margin: 6,
    borderRadius: 16,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
    backgroundColor: 'white',
  },
  compactCardButton: {
    flex: 1,
  },
  compactMealImage: {
    width: '100%' as const,
    height: 160,
    justifyContent: 'space-between' as const,
  },
  compactImageStyle: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  compactImageOverlay: {
    flex: 1,
    justifyContent: 'space-between' as const,
    padding: 12,
  },
  compactMealBadge: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start' as const,
  },
  compactMealType: {
    fontSize: 12,
    fontWeight: '600' as const,
    color: 'white',
  },
  compactMealInfo: {
    gap: 4,
  },
  compactMealName: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: 'white',
    lineHeight: 20,
  },
  compactMealMeta: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  compactMealTime: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500' as const,
  },
  compactMealCalories: {
    fontSize: 12,
    color: 'white',
    fontWeight: '700' as const,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  compactActions: {
    flexDirection: 'row' as const,
    padding: 8,
    gap: 8,
  },
  compactPrimaryAction: {
    flex: 1,
    backgroundColor: '#6B7C5A',
    borderRadius: 8,
    paddingVertical: 8,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  compactSecondaryAction: {
    flex: 1,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 8,
    paddingVertical: 8,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },

  // Quick Stats
  quickStats: {
    flexDirection: 'row' as const,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center' as const,
    justifyContent: 'space-around' as const,
  },
  statItem: {
    alignItems: 'center' as const,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: 'white',
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },

  // Action Section
  actionSection: {
    padding: 20,
    paddingTop: 0,
    backgroundColor: 'rgba(107, 124, 90, 0.03)',
  },
  actionGrid: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  modernCreateButton: {
    flex: 2,
    borderRadius: 20,
    overflow: 'hidden' as const,
    backgroundColor: '#6B7C5A',
  },
  modernCreateButtonDisabled: {
    opacity: 0.7,
  },
  createButtonGradient: {
    padding: 20,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    flexDirection: 'row' as const,
    gap: 8,
    backgroundColor: '#6B7C5A',
  },
  modernCreateText: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: 'white',
  },
  modernCustomizeButton: {
    flex: 1,
    backgroundColor: '#8B9A7A',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  customizeButtonContent: {
    alignItems: 'center' as const,
    gap: 4,
  },
  customizeText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: 'white',
  },

  // Plan Container
  modernPlanContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    marginHorizontal: 16,
    marginTop: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  planHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 20,
  },
  modernPlanTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#1F2937',
  },
  viewModeToggle: {
    flexDirection: 'row' as const,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 12,
    padding: 4,
  },
  toggleButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  toggleButtonActive: {
    backgroundColor: '#6B7C5A',
  },

  // Removed duplicate day card styles - using new ones above

  // Removed duplicate meal card styles - using new ones above

  // Empty State
  modernEmptyState: {
    margin: 20,
    borderRadius: 24,
    overflow: 'hidden' as const,
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
  },
  emptyStateGradient: {
    padding: 40,
    alignItems: 'center' as const,
  },
  modernEmptyTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#1F2937',
    marginTop: 20,
    marginBottom: 8,
  },
  modernEmptySubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center' as const,
    lineHeight: 24,
    marginBottom: 24,
  },
  emptyStateButton: {
    borderRadius: 16,
    overflow: 'hidden' as const,
    backgroundColor: '#6B7C5A',
  },
  emptyButtonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
    backgroundColor: '#6B7C5A',
  },
  emptyButtonText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: 'white',
  },

  // Simple Header
  simpleHeader: {
    backgroundColor: '#6B7C5A',
    paddingBottom: 20,
  },

  // Simple Modal Styles
  simpleModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    padding: 20,
  },
  simpleModalContainer: {
    width: '100%' as const,
    height: height * 0.85,
    backgroundColor: 'white',
    borderRadius: 20,
    overflow: 'hidden' as const,
    marginTop: 60,
  },
  simpleModalHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  simpleModalTitle: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: '#1F2937',
  },
  simpleModalClose: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  simpleModalContent: {
    flex: 1,
    padding: 20,
  },
  simpleSection: {
    marginBottom: 24,
  },
  simpleSectionTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#1F2937',
    marginBottom: 12,
  },
  simpleTextInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    minHeight: 80,
    textAlignVertical: 'top' as const,
  },
  simpleRow: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  simpleInputGroup: {
    flex: 1,
  },
  simpleLabel: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: '#6B7280',
    marginBottom: 8,
  },
  simpleNumberInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    textAlign: 'center' as const,
  },
  simpleTagsContainer: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    gap: 8,
  },
  simpleTag: {
    backgroundColor: '#F3F4F6',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  simpleTagSelected: {
    backgroundColor: '#6B7C5A',
  },
  simpleTagText: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: '#6B7C5A',
  },
  simpleTagTextSelected: {
    color: 'white',
  },
  simpleCountContainer: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  simpleCountButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F3F4F6',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  simpleCountButtonSelected: {
    backgroundColor: '#6B7C5A',
  },
  simpleCountText: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },
  simpleCountTextSelected: {
    color: 'white',
  },
  simpleModalActions: {
    flexDirection: 'row' as const,
    padding: 20,
    paddingTop: 16,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  simpleCancelButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  simpleCancelText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#6B7280',
  },
  simpleSaveButton: {
    flex: 2,
    backgroundColor: '#6B7C5A',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  simpleSaveText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: 'white',
  },
  modalGradient: {
    flex: 1,
  },
  modernModalHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'flex-start' as const,
    padding: 24,
    paddingBottom: 16,
  },
  modalHeaderContent: {
    flex: 1,
  },
  modernModalTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#1F2937',
    marginBottom: 4,
  },
  modernModalSubtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  modernModalClose: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  modernModalContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  modernSection: {
    marginBottom: 24,
  },
  modernSectionTitle: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: '#1F2937',
    marginBottom: 12,
  },
  modernTextInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    minHeight: 80,
    textAlignVertical: 'top' as const,
  },
  nutritionGrid: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  nutritionItem: {
    flex: 1,
  },
  nutritionLabel: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: '#6B7280',
    marginBottom: 8,
  },
  nutritionInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    textAlign: 'center' as const,
  },
  modernTagsGrid: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    gap: 8,
  },
  modernTag: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    backgroundColor: '#F3F4F6',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 6,
  },
  modernTagSelected: {
    backgroundColor: '#6B7C5A',
  },
  modernTagText: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: '#6B7C5A',
  },
  modernTagTextSelected: {
    color: 'white',
  },
  preferenceRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 16,
  },
  preferenceLabel: {
    fontSize: 16,
    fontWeight: '500' as const,
    color: '#1F2937',
  },
  modernCountButtons: {
    flexDirection: 'row' as const,
    gap: 8,
  },
  modernCountButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  modernCountButtonSelected: {
    backgroundColor: '#6B7C5A',
  },
  modernCountText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },
  modernCountTextSelected: {
    color: 'white',
  },
  modernModalActions: {
    flexDirection: 'row' as const,
    padding: 24,
    paddingTop: 16,
    gap: 12,
  },
  modernCancelButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  modernCancelText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#6B7280',
  },
  modernSaveButton: {
    flex: 2,
    borderRadius: 16,
    overflow: 'hidden' as const,
    backgroundColor: '#6B7C5A',
  },
  saveButtonGradient: {
    paddingVertical: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    backgroundColor: '#6B7C5A',
  },
  modernSaveText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: 'white',
  },
};

export default PlanScreenModern;

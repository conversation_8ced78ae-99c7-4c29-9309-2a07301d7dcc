import React, { useState } from 'react';
import { View, Text, StyleSheet, Switch } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Animated, { SlideInLeft } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';

import { OnboardingStackParamList } from './OnboardingNavigator';
import { useOnboarding } from '../../contexts/OnboardingContext';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';

type NotificationSettingsScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'NotificationSettings'>;

const NotificationSettingsScreen: React.FC = () => {
  const navigation = useNavigation<NotificationSettingsScreenNavigationProp>();
  const { data, updateData, nextStep, previousStep } = useOnboarding();

  const [settings, setSettings] = useState({
    mealReminders: data.mealReminders,
    waterReminders: data.waterReminders,
    progressUpdates: data.progressUpdates,
  });

  const updateSetting = (key: keyof typeof settings, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleNext = () => {
    updateData('mealReminders', settings.mealReminders);
    updateData('waterReminders', settings.waterReminders);
    updateData('progressUpdates', settings.progressUpdates);
    
    nextStep();
    navigation.navigate('ProfilePicture');
  };

  const handleBack = () => {
    previousStep();
    navigation.goBack();
  };

  const notificationOptions = [
    {
      key: 'mealReminders' as const,
      title: 'Meal Reminders',
      description: 'Get reminded to log your meals',
      icon: 'restaurant' as const,
      color: '#10B981',
    },
    {
      key: 'waterReminders' as const,
      title: 'Water Reminders',
      description: 'Stay hydrated with regular reminders',
      icon: 'water' as const,
      color: '#3B82F6',
    },
    {
      key: 'progressUpdates' as const,
      title: 'Progress Updates',
      description: 'Weekly summaries of your progress',
      icon: 'trending-up' as const,
      color: '#8B5CF6',
    },
  ];

  return (
    <OnboardingLayout
      title="Notification Settings"
      subtitle="Choose how you'd like to stay motivated"
      onNext={handleNext}
      onBack={handleBack}
      showSkip={true}
      onSkip={handleNext}
    >
      <View style={styles.container}>
        {notificationOptions.map((option, index) => (
          <Animated.View
            key={option.key}
            entering={SlideInLeft.delay(200 + index * 100).duration(600)}
            style={styles.settingCard}
          >
            <View style={styles.settingContent}>
              <View style={[styles.iconContainer, { backgroundColor: `${option.color}20` }]}>
                <Ionicons name={option.icon} size={24} color={option.color} />
              </View>
              
              <View style={styles.textContainer}>
                <Text style={styles.settingTitle}>{option.title}</Text>
                <Text style={styles.settingDescription}>{option.description}</Text>
              </View>

              <Switch
                value={settings[option.key]}
                onValueChange={(value) => updateSetting(option.key, value)}
                trackColor={{ false: 'rgba(255, 255, 255, 0.3)', true: '#6B7C5A' }}
                thumbColor={settings[option.key] ? 'white' : 'rgba(255, 255, 255, 0.8)'}
                ios_backgroundColor="rgba(255, 255, 255, 0.3)"
              />
            </View>
          </Animated.View>
        ))}
      </View>
    </OnboardingLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 32,
  },
  settingCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 24,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  textContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  settingDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    lineHeight: 20,
  },
});

export default NotificationSettingsScreen;

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Vibration,
  Alert,
  Pressable,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Haptics from 'expo-haptics';
import { Ionicons } from '@expo/vector-icons';
import Animated, { 
  FadeInUp, 
  FadeInDown, 
  FadeInLeft, 
  FadeInRight,
  SlideInUp,
  SlideInDown,
  ZoomIn,
  ZoomOut,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
  withRepeat,
  Easing,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useRoute, useNavigation } from '@react-navigation/native';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernModal } from '../components/ModernModal';

const { width, height } = Dimensions.get('window');

interface TimerDisplayProps {
  minutes: number;
  seconds: number;
  isRunning: boolean;
  progress: number;
  onPress: () => void;
}

interface StepNavigatorProps {
  currentStep: number;
  totalSteps: number;
  completedSteps: boolean[];
  onStepPress: (step: number) => void;
}

interface QuickTimerProps {
  label: string;
  minutes: number;
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  index: number;
}

// Modern Timer Display Component
const TimerDisplay: React.FC<TimerDisplayProps> = ({
  minutes,
  seconds,
  isRunning,
  progress,
  onPress,
}) => {
  const scale = useSharedValue(1);
  const pulseOpacity = useSharedValue(0);

  useEffect(() => {
    if (isRunning) {
      pulseOpacity.value = withRepeat(
        withSequence(
          withTiming(0.3, { duration: 1000 }),
          withTiming(0, { duration: 1000 })
        ),
        -1,
        false
      );
    } else {
      pulseOpacity.value = withTiming(0, { duration: 300 });
    }
  }, [isRunning]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const pulseStyle = useAnimatedStyle(() => ({
    opacity: pulseOpacity.value,
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    runOnJS(onPress)();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
  };

  const formatTime = (mins: number, secs: number) => {
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Animated.View style={[styles.timerContainer, animatedStyle]}>
      <View style={styles.timerBackground}>
        <Animated.View style={[styles.timerPulse, pulseStyle]} />
        
        {/* Progress Ring */}
        <View style={styles.progressRing}>
          <View style={[styles.progressBackground]} />
          <View 
            style={[
              styles.progressFill,
              { 
                transform: [{ rotate: `${progress * 360}deg` }],
              }
            ]} 
          />
        </View>

        <Pressable style={styles.timerButton} onPress={handlePress}>
          <BlurView intensity={20} style={styles.timerGradient}>
            <LinearGradient
              colors={isRunning ?
                ['rgba(239, 68, 68, 0.1)', 'rgba(239, 68, 68, 0.05)'] :
                ['rgba(107, 124, 90, 0.1)', 'rgba(107, 124, 90, 0.05)']}
              style={styles.timerInnerGradient}
            >
              <Text style={styles.timerText}>{formatTime(minutes, seconds)}</Text>
              <Text style={styles.timerLabel}>
                {isRunning ? 'Tap to Pause' : 'Tap to Start'}
              </Text>
            </LinearGradient>
          </BlurView>
        </Pressable>
      </View>
    </Animated.View>
  );
};

// Step Navigator Component
const StepNavigator: React.FC<StepNavigatorProps> = ({
  currentStep,
  totalSteps,
  completedSteps,
  onStepPress,
}) => {
  return (
    <View style={styles.stepNavigator}>
      <Text style={styles.stepNavigatorTitle}>
        Step {currentStep + 1} of {totalSteps}
      </Text>
      
      <View style={styles.stepIndicators}>
        {Array.from({ length: totalSteps }, (_, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.stepIndicator,
              index === currentStep && styles.stepIndicatorActive,
              completedSteps[index] && styles.stepIndicatorCompleted,
            ]}
            onPress={() => onStepPress(index)}
          >
            {completedSteps[index] ? (
              <Ionicons name="checkmark" size={12} color={Colors.brandForeground} />
            ) : (
              <Text style={[
                styles.stepIndicatorText,
                index === currentStep && styles.stepIndicatorTextActive,
              ]}>
                {index + 1}
              </Text>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

// Quick Timer Component
const QuickTimer: React.FC<QuickTimerProps> = ({ label, minutes, icon, onPress, index }) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    runOnJS(onPress)();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  return (
    <Animated.View 
      entering={ZoomIn.delay(index * 100).duration(500)}
      style={[styles.quickTimer, animatedStyle]}
    >
      <Pressable style={styles.quickTimerButton} onPress={handlePress}>
        <View style={styles.quickTimerIcon}>
          <Ionicons name={icon} size={24} color={Colors.brand} />
        </View>
        <Text style={styles.quickTimerLabel}>{label}</Text>
        <Text style={styles.quickTimerTime}>{minutes} min</Text>
      </Pressable>
    </Animated.View>
  );
};

const CookingTimerScreenModern: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  
  const recipeTitle = (route.params as any)?.recipe || 'Cooking Timer';
  const instructions = (route.params as any)?.instructions || [];
  const servings = (route.params as any)?.servings || 4;

  const [minutes, setMinutes] = useState(15);
  const [seconds, setSeconds] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [totalTime, setTotalTime] = useState(15 * 60);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<boolean[]>(
    new Array(instructions.length).fill(false)
  );
  const [showQuickTimers, setShowQuickTimers] = useState(false);
  const [showStepDetail, setShowStepDetail] = useState(false);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const quickTimers = [
    { label: 'Boil Water', minutes: 5, icon: 'water' as const },
    { label: 'Sauté', minutes: 8, icon: 'flame' as const },
    { label: 'Simmer', minutes: 15, icon: 'time' as const },
    { label: 'Bake', minutes: 25, icon: 'restaurant' as const },
    { label: 'Rest', minutes: 10, icon: 'pause' as const },
    { label: 'Chill', minutes: 30, icon: 'snow' as const },
  ];

  useEffect(() => {
    if (isRunning && (minutes > 0 || seconds > 0)) {
      intervalRef.current = setInterval(() => {
        if (seconds > 0) {
          setSeconds(seconds - 1);
        } else if (minutes > 0) {
          setMinutes(minutes - 1);
          setSeconds(59);
        }
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    // Timer finished
    if (isRunning && minutes === 0 && seconds === 0) {
      setIsRunning(false);
      Vibration.vibrate([0, 500, 200, 500]);
      Alert.alert(
        'Timer Finished!',
        'Your cooking timer has completed.',
        [{ text: 'OK', onPress: () => {} }]
      );
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, minutes, seconds]);

  const toggleTimer = () => {
    setIsRunning(!isRunning);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setMinutes(Math.floor(totalTime / 60));
    setSeconds(totalTime % 60);
  };

  const setQuickTimer = (mins: number) => {
    setMinutes(mins);
    setSeconds(0);
    setTotalTime(mins * 60);
    setIsRunning(false);
    setShowQuickTimers(false);
  };

  // Get adaptive timing for cooking steps
  const getStepTiming = (instruction: string): number => {
    const lowerInstruction = instruction.toLowerCase();

    // Cooking method timing patterns
    if (lowerInstruction.includes('boil') || lowerInstruction.includes('bring to boil')) return 8;
    if (lowerInstruction.includes('simmer')) return 15;
    if (lowerInstruction.includes('sauté') || lowerInstruction.includes('fry')) return 5;
    if (lowerInstruction.includes('bake') || lowerInstruction.includes('oven')) return 25;
    if (lowerInstruction.includes('rest') || lowerInstruction.includes('cool')) return 10;
    if (lowerInstruction.includes('marinate')) return 30;
    if (lowerInstruction.includes('chill') || lowerInstruction.includes('refrigerate')) return 60;
    if (lowerInstruction.includes('mix') || lowerInstruction.includes('stir')) return 2;
    if (lowerInstruction.includes('chop') || lowerInstruction.includes('dice')) return 5;
    if (lowerInstruction.includes('season') || lowerInstruction.includes('add')) return 1;

    // Time-specific patterns
    const timeMatch = lowerInstruction.match(/(\d+)\s*(minute|min|hour|hr)/);
    if (timeMatch) {
      const value = parseInt(timeMatch[1]);
      const unit = timeMatch[2];
      return unit.includes('hour') || unit.includes('hr') ? value * 60 : value;
    }

    // Default timing
    return 10;
  };

  const nextStep = () => {
    if (currentStep < instructions.length - 1) {
      const newCompleted = [...completedSteps];
      newCompleted[currentStep] = true;
      setCompletedSteps(newCompleted);
      setCurrentStep(currentStep + 1);

      // Auto-set timer for next step
      const nextInstruction = instructions[currentStep + 1];
      if (nextInstruction) {
        const stepTime = getStepTiming(nextInstruction);
        setMinutes(stepTime);
        setSeconds(0);
        setTotalTime(stepTime * 60);
        setIsRunning(false);
      }
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    setCurrentStep(step);
  };

  const getProgress = () => {
    const elapsed = totalTime - (minutes * 60 + seconds);
    return totalTime > 0 ? elapsed / totalTime : 0;
  };

  const getCurrentInstruction = () => {
    return instructions[currentStep] || 'All steps completed!';
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {/* Fully Scrollable Content */}
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Header Section (now scrollable) */}
        <Animated.View entering={FadeInDown.duration(600)} style={styles.headerSection}>
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color="#6B7C5A" />
            </TouchableOpacity>

            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>{recipeTitle}</Text>
              <Text style={styles.headerSubtitle}>Cooking for {servings} servings</Text>
            </View>

            <TouchableOpacity
              style={styles.menuButton}
              onPress={() => setShowQuickTimers(true)}
            >
              <Ionicons name="timer" size={24} color="#6B7C5A" />
            </TouchableOpacity>
          </View>
        </Animated.View>
        {/* Timer Display */}
        <Animated.View entering={ZoomIn.delay(200).duration(800)} style={styles.timerSection}>
          <TimerDisplay
            minutes={minutes}
            seconds={seconds}
            isRunning={isRunning}
            progress={getProgress()}
            onPress={toggleTimer}
          />

          <View style={styles.timerControls}>
            <ModernButton
              title="Reset"
              onPress={resetTimer}
              variant="outline"
              size="md"
              icon="refresh"
              style={styles.timerControlButton}
            />
            <ModernButton
              title="Quick Timers"
              onPress={() => setShowQuickTimers(true)}
              variant="secondary"
              size="md"
              icon="timer"
              style={styles.timerControlButton}
            />
          </View>
        </Animated.View>

        {/* Step Navigation */}
        {instructions.length > 0 && (
          <Animated.View entering={SlideInUp.delay(400).duration(600)} style={styles.stepSection}>
            <StepNavigator
              currentStep={currentStep}
              totalSteps={instructions.length}
              completedSteps={completedSteps}
              onStepPress={goToStep}
            />
          </Animated.View>
        )}

        {/* Current Step */}
        {instructions.length > 0 && (
          <Animated.View entering={SlideInUp.delay(600).duration(600)} style={styles.currentStepSection}>
            <ModernCard variant="glass" title="" style={styles.currentStepCard}>
              <TouchableOpacity
                style={styles.stepDetailButton}
                onPress={() => setShowStepDetail(true)}
              >
                <View style={styles.stepHeader}>
                  <Text style={styles.stepTitle}>Current Step</Text>
                  <Ionicons name="expand" size={20} color={Colors.brand} />
                </View>

                <Text style={styles.currentStepText}>
                  {getCurrentInstruction()}
                </Text>

                <View style={styles.stepActions}>
                  <ModernButton
                    title="Previous"
                    onPress={previousStep}
                    variant="outline"
                    size="sm"
                    icon="chevron-back"
                    disabled={currentStep === 0}
                    style={styles.stepActionButton}
                  />
                  <ModernButton
                    title="Complete Step"
                    onPress={nextStep}
                    variant="primary"
                    size="sm"
                    icon="checkmark"
                    disabled={currentStep >= instructions.length - 1}
                    style={[
                      styles.stepActionButton,
                      !completedSteps[currentStep] && styles.completeStepButton
                    ]}
                  />
                </View>
              </TouchableOpacity>
            </ModernCard>
          </Animated.View>
        )}

        {/* Progress Summary */}
        <Animated.View entering={SlideInUp.delay(800).duration(600)} style={styles.progressSection}>
          <ModernCard variant="default" title="" style={styles.progressCard}>
            <Text style={styles.progressTitle}>Cooking Progress</Text>
            <View style={styles.progressStats}>
              <View style={styles.progressStat}>
                <Text style={styles.progressStatValue}>
                  {completedSteps.filter(Boolean).length}
                </Text>
                <Text style={styles.progressStatLabel}>Steps Done</Text>
              </View>
              <View style={styles.progressStat}>
                <Text style={styles.progressStatValue}>
                  {instructions.length - completedSteps.filter(Boolean).length}
                </Text>
                <Text style={styles.progressStatLabel}>Remaining</Text>
              </View>
              <View style={styles.progressStat}>
                <Text style={styles.progressStatValue}>
                  {Math.round((completedSteps.filter(Boolean).length / instructions.length) * 100)}%
                </Text>
                <Text style={styles.progressStatLabel}>Complete</Text>
              </View>
            </View>
          </ModernCard>
        </Animated.View>
      </ScrollView>

      {/* Quick Timers Modal */}
      <ModernModal
        visible={showQuickTimers}
        onClose={() => setShowQuickTimers(false)}
        title="Quick Timers"
        variant="fullscreen"
      >
        <View style={styles.quickTimersGrid}>
          {quickTimers.map((timer, index) => (
            <QuickTimer
              key={timer.label}
              label={timer.label}
              minutes={timer.minutes}
              icon={timer.icon}
              onPress={() => setQuickTimer(timer.minutes)}
              index={index}
            />
          ))}
        </View>
      </ModernModal>

      {/* Step Detail Modal */}
      <ModernModal
        visible={showStepDetail}
        onClose={() => setShowStepDetail(false)}
        title={`Step ${currentStep + 1} Details`}
        variant="center"
        size="lg"
      >
        <View style={styles.stepDetailContent}>
          <Text style={styles.stepDetailText}>
            {getCurrentInstruction()}
          </Text>

          <View style={styles.stepDetailActions}>
            <ModernButton
              title="Set Timer for Step"
              onPress={() => {
                setQuickTimer(15);
                setShowStepDetail(false);
              }}
              variant="primary"
              size="md"
              icon="timer"
              fullWidth
            />
          </View>
        </View>
      </ModernModal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAF9',
  },
  scrollContainer: {
    flex: 1,
    paddingTop: 80, // Consistent top padding
  },
  headerSection: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
  },

  // Apple-Inspired Header with Glassmorphism
  header: {
    paddingTop: 80,
    paddingBottom: 32,
    paddingHorizontal: 24,
    position: 'relative',
    overflow: 'hidden',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.2)',
  },
  headerText: {
    flex: 1,
    marginHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: '#6B7C5A',
    marginBottom: 4,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(107, 124, 90, 0.7)',
    textAlign: 'center',
    fontWeight: '500',
  },
  menuButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.2)',
  },

  // Main Content with Modern Spacing
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 0,
  },

  // Apple-Inspired Timer Section
  timerSection: {
    alignItems: 'center',
    marginBottom: 48,
    paddingVertical: 24,
  },
  timerContainer: {
    marginBottom: 32,
    position: 'relative',
  },
  timerBackground: {
    width: 280,
    height: 280,
    borderRadius: 140,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    borderWidth: 2,
    borderColor: '#6B7C5A',
  },
  timerPulse: {
    position: 'absolute',
    width: 340,
    height: 340,
    borderRadius: 170,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    top: -10,
    left: -10,
  },
  progressRing: {
    position: 'absolute',
    width: 320,
    height: 320,
    borderRadius: 160,
  },
  progressBackground: {
    position: 'absolute',
    width: 320,
    height: 320,
    borderRadius: 160,
    borderWidth: 6,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  progressFill: {
    position: 'absolute',
    width: 320,
    height: 320,
    borderRadius: 160,
    borderWidth: 6,
    borderColor: '#6B7C5A',
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  timerButton: {
    width: 280,
    height: 280,
    borderRadius: 140,
    overflow: 'hidden',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 20,
  },
  timerGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  timerInnerGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  timerText: {
    fontSize: 56,
    fontWeight: '900',
    color: '#6B7C5A',
    marginBottom: 8,
    letterSpacing: -1,
  },
  timerLabel: {
    fontSize: 18,
    color: 'rgba(107, 124, 90, 0.7)',
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  timerControls: {
    flexDirection: 'row',
    gap: 16,
    paddingHorizontal: 20,
  },
  timerControlButton: {
    flex: 1,
    borderRadius: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },

  // Modern Step Navigation with Glassmorphism
  stepSection: {
    marginBottom: 32,
  },
  stepNavigator: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 32,
    padding: 24,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 12,
  },
  stepNavigatorTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#6B7C5A',
    textAlign: 'center',
    marginBottom: 20,
    letterSpacing: 0.3,
  },
  stepIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: 12,
  },
  stepIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(107, 124, 90, 0.08)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(107, 124, 90, 0.15)',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  stepIndicatorActive: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 6,
    transform: [{ scale: 1.1 }],
  },
  stepIndicatorCompleted: {
    backgroundColor: '#22c55e',
    borderColor: '#22c55e',
    shadowColor: '#22c55e',
    shadowOpacity: 0.2,
  },
  stepIndicatorText: {
    fontSize: 16,
    fontWeight: '800',
    color: '#6B7C5A',
  },
  stepIndicatorTextActive: {
    color: 'white',
    fontSize: 17,
  },

  // Modern Current Step Section
  currentStepSection: {
    marginBottom: 32,
  },
  currentStepCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 32,
    padding: 28,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 12,
  },
  stepDetailButton: {
    flex: 1,
  },
  stepHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#6B7C5A',
    letterSpacing: 0.3,
  },
  currentStepText: {
    fontSize: 17,
    color: '#374151',
    lineHeight: 26,
    marginBottom: 24,
    fontWeight: '500',
  },
  stepActions: {
    flexDirection: 'row',
    gap: 16,
  },
  stepActionButton: {
    flex: 1,
    borderRadius: 24,
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  completeStepButton: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  disabledButton: {
    backgroundColor: 'rgba(107, 124, 90, 0.3)',
    borderColor: 'rgba(107, 124, 90, 0.3)',
  },

  // Modern Progress Section
  progressSection: {
    marginBottom: 32,
  },
  progressCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 32,
    padding: 28,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 12,
  },
  progressTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#6B7C5A',
    marginBottom: 24,
    textAlign: 'center',
    letterSpacing: 0.3,
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  progressStat: {
    alignItems: 'center',
  },
  progressStatValue: {
    fontSize: 32,
    fontWeight: '900',
    color: '#6B7C5A',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  progressStatLabel: {
    fontSize: 14,
    color: 'rgba(107, 124, 90, 0.7)',
    fontWeight: '600',
    letterSpacing: 0.5,
    textTransform: 'uppercase',
  },

  // Modern Quick Timers with Glassmorphism
  quickTimersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    padding: 24,
    justifyContent: 'space-between',
  },
  quickTimer: {
    width: (width - 24 * 3 - 16) / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 28,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  quickTimerButton: {
    padding: 20,
    alignItems: 'center',
  },
  quickTimerIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.15)',
  },
  quickTimerLabel: {
    fontSize: 16,
    fontWeight: '700',
    color: '#6B7C5A',
    marginBottom: 6,
    textAlign: 'center',
    letterSpacing: 0.3,
  },
  quickTimerTime: {
    fontSize: 14,
    color: 'rgba(107, 124, 90, 0.7)',
    fontWeight: '600',
    letterSpacing: 0.5,
  },

  // Modern Step Detail Modal
  stepDetailContent: {
    padding: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
  },
  stepDetailText: {
    fontSize: 18,
    color: '#374151',
    lineHeight: 28,
    marginBottom: 32,
    textAlign: 'center',
    fontWeight: '500',
  },
  stepDetailActions: {
    gap: 16,
  },
});

export default CookingTimerScreenModern;

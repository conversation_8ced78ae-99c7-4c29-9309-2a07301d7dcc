import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useProfile } from './ProfileContext';

export interface OnboardingData {
  // Personal Information
  name: string;
  age: number;
  gender: 'male' | 'female' | 'other' | '';
  height: number; // in cm
  weight: number; // in kg
  
  // Health Goals
  weightGoal: 'lose' | 'maintain' | 'gain' | '';
  targetWeight: number;
  fitnessObjectives: string[];
  healthConditions: string[];
  
  // Dietary Preferences
  dietaryPreferences: string[];
  
  // Allergens & Restrictions
  allergens: string[];
  dietaryRestrictions: string[];
  
  // Cuisine Preferences
  cuisinePreferences: string[];
  
  // Activity Level
  activityLevel: 'sedentary' | 'lightly_active' | 'moderately_active' | 'very_active' | '';
  
  // Nutrition Goals
  caloriesGoal: number;
  proteinGoal: number;
  carbsPercentage: number;
  fatPercentage: number;
  
  // Notification Settings
  mealReminders: boolean;
  waterReminders: boolean;
  progressUpdates: boolean;
  
  // Profile Picture
  profileImage: string | null;
  
  // Progress
  currentStep: number;
  isComplete: boolean;
}

interface OnboardingContextType {
  data: OnboardingData;
  updateData: (field: keyof OnboardingData, value: any) => void;
  updateMultipleFields: (updates: Partial<OnboardingData>) => void;
  nextStep: () => void;
  previousStep: () => void;
  goToStep: (step: number) => void;
  saveProgress: () => Promise<void>;
  loadProgress: () => Promise<void>;
  completeOnboarding: () => Promise<void>;
  getProgressPercentage: () => number;
  canGoNext: () => boolean;
  canGoPrevious: () => boolean;
}

const defaultData: OnboardingData = {
  name: '',
  age: 0,
  gender: '',
  height: 0,
  weight: 0,
  weightGoal: '',
  targetWeight: 0,
  fitnessObjectives: [],
  healthConditions: [],
  dietaryPreferences: [],
  allergens: [],
  dietaryRestrictions: [],
  cuisinePreferences: [],
  activityLevel: '',
  caloriesGoal: 2000,
  proteinGoal: 150,
  carbsPercentage: 45,
  fatPercentage: 30,
  mealReminders: true,
  waterReminders: true,
  progressUpdates: true,
  profileImage: null,
  currentStep: 0,
  isComplete: false,
};

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export const OnboardingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [data, setData] = useState<OnboardingData>(defaultData);
  const { updateProfileBatch } = useProfile();
  
  const totalSteps = 11; // Total number of onboarding screens

  useEffect(() => {
    loadProgress();
  }, []);

  const updateData = (field: keyof OnboardingData, value: any) => {
    setData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateMultipleFields = (updates: Partial<OnboardingData>) => {
    setData(prev => ({
      ...prev,
      ...updates
    }));
  };

  const nextStep = () => {
    if (data.currentStep < totalSteps - 1) {
      updateData('currentStep', data.currentStep + 1);
    }
  };

  const previousStep = () => {
    if (data.currentStep > 0) {
      updateData('currentStep', data.currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 0 && step < totalSteps) {
      updateData('currentStep', step);
    }
  };

  const saveProgress = async () => {
    try {
      await AsyncStorage.setItem('onboardingProgress', JSON.stringify(data));
    } catch (error) {
      console.error('Error saving onboarding progress:', error);
    }
  };

  const loadProgress = async () => {
    try {
      const savedProgress = await AsyncStorage.getItem('onboardingProgress');
      if (savedProgress) {
        const parsedData = JSON.parse(savedProgress);
        setData(parsedData);
      }
    } catch (error) {
      console.error('Error loading onboarding progress:', error);
    }
  };

  const completeOnboarding = async () => {
    try {
      // Update profile context with all onboarding data
      const profileUpdates = {
        name: data.name,
        age: data.age,
        gender: data.gender,
        height: data.height,
        weight: data.weight,
        caloriesGoal: data.caloriesGoal,
        proteinGoal: data.proteinGoal,
        waterGoal: 8, // Default water goal
        stepsGoal: 10000, // Default steps goal
        dietaryPreferences: data.dietaryPreferences,
        allergies: data.allergens, // Map allergens to allergies
        preferredCuisines: data.cuisinePreferences, // Map cuisinePreferences to preferredCuisines
        activityLevel: data.activityLevel,
        weightGoal: data.weightGoal,
        targetWeight: data.targetWeight,
        fitnessObjectives: data.fitnessObjectives,
        healthConditions: data.healthConditions,
        notificationSettings: {
          mealReminders: data.mealReminders,
          waterReminders: data.waterReminders,
          progressUpdates: data.progressUpdates,
        },
        isProfileComplete: true,
      };

      // Update profile context with batch update
      await updateProfileBatch(profileUpdates);

      // Save profile image if provided
      if (data.profileImage) {
        await AsyncStorage.setItem('profileImage', data.profileImage);
      }

      // Mark onboarding as complete
      updateData('isComplete', true);
      await AsyncStorage.setItem('onboardingComplete', 'true');
      await AsyncStorage.removeItem('onboardingProgress');

    } catch (error) {
      console.error('Error completing onboarding:', error);
    }
  };

  const getProgressPercentage = () => {
    return Math.round((data.currentStep / (totalSteps - 1)) * 100);
  };

  const canGoNext = () => {
    return data.currentStep < totalSteps - 1;
  };

  const canGoPrevious = () => {
    return data.currentStep > 0;
  };

  const contextValue: OnboardingContextType = {
    data,
    updateData,
    updateMultipleFields,
    nextStep,
    previousStep,
    goToStep,
    saveProgress,
    loadProgress,
    completeOnboarding,
    getProgressPercentage,
    canGoNext,
    canGoPrevious,
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};

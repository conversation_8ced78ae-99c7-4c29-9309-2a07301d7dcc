import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getDefaultProfile, UserProfile as ImportedUserProfile } from '../constants/UserData';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Use the interface from UserData.ts
export type UserProfile = ImportedUserProfile;

// Meal interface for detailed tracking
export interface MealEntry {
  id: string;
  name: string;
  time: string;
  calories: number;
  protein?: number; // Add protein tracking
  carbs?: number;   // Add carbs tracking
  fat?: number;     // Add fat tracking
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  timestamp: number;
}

// Daily tracking data interface
export interface DailyData {
  date: string;
  caloriesConsumed: number;
  proteinConsumed: number;
  waterConsumed: number;
  stepsCompleted: number;
  workoutsCompleted: number;
  mealsLogged: string[];
  recentMeals: MealEntry[];
  weight?: number;
  mood?: string;
  energyLevel?: number; // 1-10
  sleepHours?: number;
}

// Context interface
interface ProfileContextType {
  profile: UserProfile;
  dailyData: DailyData;
  updateProfile: (field: keyof UserProfile, value: any) => Promise<void>;
  updateProfileBatch: (updates: Partial<UserProfile>) => Promise<void>;
  updateDailyData: (field: keyof DailyData, value: any) => void;
  calculateBMI: () => number;
  calculateCaloriesRemaining: () => number;
  calculateProteinRemaining: () => number;
  calculateWaterRemaining: () => number;
  calculateStepsRemaining: () => number;
  getProgressPercentage: (type: 'calories' | 'protein' | 'water' | 'steps') => number;
  addMealToLog: (mealName: string) => void;
  addRecentMeal: (meal: Omit<MealEntry, 'id' | 'timestamp'>) => Promise<void>;
  getRecentMeals: () => MealEntry[];
  clearRecentMeals: () => Promise<void>;
  incrementWater: () => void;
  updateWeight: (newWeight: number) => void;
  getHealthScore: () => number;
  getWeeklyAverage: (metric: string) => number;
  recalculateNutrition: () => void;
}

// Create context
const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

// Provider component
export const ProfileProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [profile, setProfile] = useState<UserProfile>(getDefaultProfile());
  const [dailyData, setDailyData] = useState<DailyData>({
    date: new Date().toISOString().split('T')[0],
    caloriesConsumed: 0, // Start with zero - real data only
    proteinConsumed: 0,
    waterConsumed: 0,
    stepsCompleted: 0,
    workoutsCompleted: 0,
    mealsLogged: [], // Empty array - no mock meals
    recentMeals: [], // Empty array - will be loaded from AsyncStorage
    weight: 0, // Will be set from profile
    mood: '',
    energyLevel: 0,
    sleepHours: 0,
  });

  // Initialize profile from storage or create new one
  useEffect(() => {
    initializeProfile();
  }, []);

  const initializeProfile = async () => {
    try {
      // Load saved profile from AsyncStorage
      const savedProfile = await AsyncStorage.getItem('userProfile');
      let userProfile: UserProfile;

      if (savedProfile) {
        // Use saved profile data
        userProfile = JSON.parse(savedProfile);
        console.log('Loaded saved profile:', userProfile);
      } else {
        // Use default profile for new users
        userProfile = getDefaultProfile();
        console.log('Using default profile for new user');
      }

      setProfile(userProfile);

      // Load recent meals from AsyncStorage
      const storedMeals = await AsyncStorage.getItem('recentMeals');
      const recentMeals = storedMeals ? JSON.parse(storedMeals) : [];

      // Initialize daily data with profile weight and recent meals
      setDailyData(prev => ({
        ...prev,
        weight: userProfile.weight,
        recentMeals
      }));

      // Recalculate nutrition from today's meals after loading
      setTimeout(() => {
        const today = new Date().toISOString().split('T')[0];
        const todaysMeals = recentMeals.filter((meal: MealEntry) => {
          const mealDate = new Date(meal.timestamp).toISOString().split('T')[0];
          return mealDate === today;
        });

        const totalCalories = todaysMeals.reduce((sum: number, meal: MealEntry) => sum + (meal.calories || 0), 0);
        const totalProtein = todaysMeals.reduce((sum: number, meal: MealEntry) => sum + (meal.protein || 0), 0);

        if (totalCalories > 0 || totalProtein > 0) {
          setDailyData(prev => ({
            ...prev,
            caloriesConsumed: totalCalories,
            proteinConsumed: totalProtein,
            mealsLogged: todaysMeals.map((meal: MealEntry) => meal.name)
          }));
        }
      }, 100); // Small delay to ensure state is set
    } catch (error) {
      console.error('Error initializing profile:', error);
      // Fallback to default profile
      const defaultProfile = getDefaultProfile();
      setProfile(defaultProfile);
    }
  };

  // Update profile function
  const updateProfile = async (field: keyof UserProfile, value: any) => {
    const updatedProfile = {
      ...profile,
      [field]: value
    };

    // Recalculate BMI if height or weight changed
    if (field === 'height' || field === 'weight') {
      const newBMI = calculateBMI();
      updatedProfile.bmi = newBMI;
    }

    setProfile(updatedProfile);

    // Save to AsyncStorage
    try {
      await AsyncStorage.setItem('userProfile', JSON.stringify(updatedProfile));
      console.log('Profile updated and saved:', field, value);
    } catch (error) {
      console.error('Error saving profile to AsyncStorage:', error);
    }
  };

  // Batch update profile function for onboarding
  const updateProfileBatch = async (updates: Partial<UserProfile>) => {
    const updatedProfile = {
      ...profile,
      ...updates
    };

    // Recalculate BMI if height or weight changed
    if (updates.height || updates.weight) {
      const height = updates.height || profile.height;
      const weight = updates.weight || profile.weight;
      if (height && weight) {
        const heightInMeters = height / 100;
        const bmi = weight / (heightInMeters * heightInMeters);
        updatedProfile.bmi = Math.round(bmi * 10) / 10;
      }
    }

    setProfile(updatedProfile);

    // Save to AsyncStorage
    try {
      await AsyncStorage.setItem('userProfile', JSON.stringify(updatedProfile));
      console.log('Profile batch updated and saved:', updates);
    } catch (error) {
      console.error('Error saving profile batch to AsyncStorage:', error);
    }
  };

  // Update daily data function
  const updateDailyData = (field: keyof DailyData, value: any) => {
    setDailyData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Calculate BMI
  const calculateBMI = (): number => {
    const heightInMeters = profile.height / 100;
    return Math.round((profile.weight / (heightInMeters * heightInMeters)) * 10) / 10;
  };

  // Calculate remaining values
  const calculateCaloriesRemaining = (): number => {
    return Math.max(0, profile.caloriesGoal - dailyData.caloriesConsumed);
  };

  const calculateProteinRemaining = (): number => {
    return Math.max(0, profile.proteinGoal - dailyData.proteinConsumed);
  };

  const calculateWaterRemaining = (): number => {
    return Math.max(0, profile.waterGoal - dailyData.waterConsumed);
  };

  const calculateStepsRemaining = (): number => {
    return Math.max(0, profile.stepsGoal - dailyData.stepsCompleted);
  };

  // Calculate progress percentages
  const getProgressPercentage = (type: 'calories' | 'protein' | 'water' | 'steps'): number => {
    switch (type) {
      case 'calories':
        return Math.min(100, (dailyData.caloriesConsumed / profile.caloriesGoal) * 100);
      case 'protein':
        return Math.min(100, (dailyData.proteinConsumed / profile.proteinGoal) * 100);
      case 'water':
        return Math.min(100, (dailyData.waterConsumed / profile.waterGoal) * 100);
      case 'steps':
        return Math.min(100, (dailyData.stepsCompleted / profile.stepsGoal) * 100);
      default:
        return 0;
    }
  };

  // Add meal to log
  const addMealToLog = (mealName: string) => {
    setDailyData(prev => ({
      ...prev,
      mealsLogged: [...prev.mealsLogged, mealName]
    }));
  };

  // Add recent meal with AsyncStorage persistence and nutrition calculation
  const addRecentMeal = async (meal: Omit<MealEntry, 'id' | 'timestamp'>) => {
    try {
      const newMeal: MealEntry = {
        ...meal,
        id: Date.now().toString(),
        timestamp: Date.now()
      };

      const updatedMeals = [newMeal, ...dailyData.recentMeals]
        .slice(0, 10) // Keep only the 10 most recent meals
        .sort((a, b) => b.timestamp - a.timestamp); // Sort by most recent first

      // Calculate updated nutrition totals from all meals logged today
      const todaysMeals = updatedMeals.filter(m => {
        const mealDate = new Date(m.timestamp).toISOString().split('T')[0];
        const today = new Date().toISOString().split('T')[0];
        return mealDate === today;
      });

      const totalCalories = todaysMeals.reduce((sum, m) => sum + (m.calories || 0), 0);
      const totalProtein = todaysMeals.reduce((sum, m) => sum + (m.protein || 0), 0);

      // Update state with calculated nutrition
      setDailyData(prev => ({
        ...prev,
        recentMeals: updatedMeals,
        caloriesConsumed: totalCalories,
        proteinConsumed: totalProtein,
        mealsLogged: [...prev.mealsLogged, meal.name] // Also add to meals logged
      }));

      // Persist to AsyncStorage
      await AsyncStorage.setItem('recentMeals', JSON.stringify(updatedMeals));
    } catch (error) {
      console.error('Error adding recent meal:', error);
    }
  };

  // Get recent meals
  const getRecentMeals = (): MealEntry[] => {
    return dailyData.recentMeals.slice(0, 5); // Return only the 5 most recent
  };

  // Clear recent meals
  const clearRecentMeals = async () => {
    try {
      setDailyData(prev => ({
        ...prev,
        recentMeals: []
      }));
      await AsyncStorage.removeItem('recentMeals');
    } catch (error) {
      console.error('Error clearing recent meals:', error);
    }
  };

  // Increment water
  const incrementWater = () => {
    setDailyData(prev => ({
      ...prev,
      waterConsumed: Math.min(prev.waterConsumed + 1, profile.waterGoal + 2)
    }));
  };

  // Update weight
  const updateWeight = (newWeight: number) => {
    updateProfile('weight', newWeight);
    setDailyData(prev => ({
      ...prev,
      weight: newWeight
    }));
  };

  // Calculate health score
  const getHealthScore = (): number => {
    const caloriesScore = Math.min(100, getProgressPercentage('calories'));
    const proteinScore = Math.min(100, getProgressPercentage('protein'));
    const waterScore = Math.min(100, getProgressPercentage('water'));
    const stepsScore = Math.min(100, getProgressPercentage('steps'));
    
    return Math.round((caloriesScore + proteinScore + waterScore + stepsScore) / 4);
  };

  // Recalculate nutrition from all logged meals for today
  const recalculateNutrition = () => {
    const today = new Date().toISOString().split('T')[0];
    const todaysMeals = dailyData.recentMeals.filter(meal => {
      const mealDate = new Date(meal.timestamp).toISOString().split('T')[0];
      return mealDate === today;
    });

    const totalCalories = todaysMeals.reduce((sum, meal) => sum + (meal.calories || 0), 0);
    const totalProtein = todaysMeals.reduce((sum, meal) => sum + (meal.protein || 0), 0);

    setDailyData(prev => ({
      ...prev,
      caloriesConsumed: totalCalories,
      proteinConsumed: totalProtein,
      mealsLogged: todaysMeals.map(meal => meal.name)
    }));
  };

  // Get weekly average (real implementation)
  const getWeeklyAverage = (metric: string): number => {
    // In a real app, this would calculate from stored daily data
    // For now, return current day's data if available
    switch (metric) {
      case 'calories':
        return dailyData.caloriesConsumed > 0 ? dailyData.caloriesConsumed : 0;
      case 'protein':
        return dailyData.proteinConsumed > 0 ? dailyData.proteinConsumed : 0;
      case 'water':
        return dailyData.waterConsumed > 0 ? dailyData.waterConsumed : 0;
      case 'steps':
        return dailyData.stepsCompleted > 0 ? dailyData.stepsCompleted : 0;
      case 'weight':
        return dailyData.weight > 0 ? dailyData.weight : profile.weight;
      case 'sleep':
        return dailyData.sleepHours > 0 ? dailyData.sleepHours : 0;
      case 'energy':
        return dailyData.energyLevel > 0 ? dailyData.energyLevel : 0;
      default:
        return 0;
    }
  };

  const contextValue: ProfileContextType = {
    profile,
    dailyData,
    updateProfile,
    updateProfileBatch,
    updateDailyData,
    calculateBMI,
    calculateCaloriesRemaining,
    calculateProteinRemaining,
    calculateWaterRemaining,
    calculateStepsRemaining,
    getProgressPercentage,
    addMealToLog,
    addRecentMeal,
    getRecentMeals,
    clearRecentMeals,
    incrementWater,
    updateWeight,
    getHealthScore,
    getWeeklyAverage,
    recalculateNutrition,
  };

  return (
    <ProfileContext.Provider value={contextValue}>
      {children}
    </ProfileContext.Provider>
  );
};

// Custom hook to use profile context
export const useProfile = (): ProfileContextType => {
  const context = useContext(ProfileContext);
  if (!context) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
};

export default ProfileContext;

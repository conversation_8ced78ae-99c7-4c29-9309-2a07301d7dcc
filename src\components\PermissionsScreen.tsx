import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  StatusBar,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Audio } from 'expo-av';
import * as Location from 'expo-location';
import { Pedometer } from 'expo-sensors';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  ZoomIn,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

interface PermissionsScreenProps {
  onPermissionsGranted: () => void;
}

interface Permission {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  granted: boolean;
  requestFunction: () => Promise<boolean>;
}

const PermissionsScreen: React.FC<PermissionsScreenProps> = ({ onPermissionsGranted }) => {
  const [permissions, setPermissions] = useState<Permission[]>([
    {
      id: 'camera',
      title: 'Camera Access',
      description: 'Scan food items and take photos for meal tracking',
      icon: 'camera',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'microphone',
      title: 'Microphone Access',
      description: 'Voice commands and audio input for AI assistant',
      icon: 'mic',
      color: '#8B9A7A',
      granted: false,
      requestFunction: async () => {
        const { status } = await Audio.requestPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'photos',
      title: 'Photo Library',
      description: 'Select photos from your gallery for meal analysis',
      icon: 'images',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'location',
      title: 'Location Access',
      description: 'Find nearby restaurants and local nutrition data',
      icon: 'location',
      color: '#8B9A7A',
      granted: false,
      requestFunction: async () => {
        const { status } = await Location.requestForegroundPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'sensors',
      title: 'Body Sensors',
      description: 'Track heart rate and fitness data for health monitoring',
      icon: 'fitness',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        try {
          const isAvailable = await Pedometer.isAvailableAsync();
          if (!isAvailable) return false;

          const { status } = await Pedometer.requestPermissionsAsync();
          return status === 'granted';
        } catch (error) {
          return false;
        }
      },
    },
    {
      id: 'activity',
      title: 'Physical Activity',
      description: 'Monitor steps, running, and daily activity patterns',
      icon: 'walk',
      color: '#8B9A7A',
      granted: false,
      requestFunction: async () => {
        try {
          const isAvailable = await Pedometer.isAvailableAsync();
          if (!isAvailable) return false;

          const { status } = await Pedometer.requestPermissionsAsync();
          return status === 'granted';
        } catch (error) {
          return false;
        }
      },
    },
  ]);

  const [allGranted, setAllGranted] = useState(false);

  useEffect(() => {
    checkAllPermissions();
  }, []);

  const checkAllPermissions = async () => {
    const updatedPermissions = await Promise.all(
      permissions.map(async (permission) => {
        let granted = false;
        try {
          switch (permission.id) {
            case 'camera':
              const cameraStatus = await ImagePicker.getCameraPermissionsAsync();
              granted = cameraStatus.status === 'granted';
              break;
            case 'microphone':
              const audioStatus = await Audio.getPermissionsAsync();
              granted = audioStatus.status === 'granted';
              break;
            case 'photos':
              const mediaStatus = await ImagePicker.getMediaLibraryPermissionsAsync();
              granted = mediaStatus.status === 'granted';
              break;
            case 'location':
              const locationStatus = await Location.getForegroundPermissionsAsync();
              granted = locationStatus.status === 'granted';
              break;
            case 'sensors':
            case 'activity':
              const isAvailable = await Pedometer.isAvailableAsync();
              if (isAvailable) {
                const pedometerStatus = await Pedometer.getPermissionsAsync();
                granted = pedometerStatus.status === 'granted';
              } else {
                granted = false;
              }
              break;
          }
        } catch (error) {
          console.log(`Error checking ${permission.id} permission:`, error);
        }
        return { ...permission, granted };
      })
    );

    setPermissions(updatedPermissions);
    const allPermissionsGranted = updatedPermissions.every(p => p.granted);
    setAllGranted(allPermissionsGranted);
  };

  const requestPermission = async (permission: Permission) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      const granted = await permission.requestFunction();
      
      setPermissions(prev => 
        prev.map(p => 
          p.id === permission.id ? { ...p, granted } : p
        )
      );

      if (!granted) {
        Alert.alert(
          'Permission Required',
          `${permission.title} is needed for the best app experience. You can enable it later in Settings.`,
          [{ text: 'OK' }]
        );
      }

      // Check if all permissions are now granted
      const updatedPermissions = permissions.map(p => 
        p.id === permission.id ? { ...p, granted } : p
      );
      const allPermissionsGranted = updatedPermissions.every(p => p.granted);
      setAllGranted(allPermissionsGranted);
    } catch (error) {
      console.error(`Error requesting ${permission.title}:`, error);
      Alert.alert('Error', `Failed to request ${permission.title}. Please try again.`);
    }
  };

  const handleContinue = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    onPermissionsGranted();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
          <Animated.View entering={ZoomIn.delay(400).duration(600)} style={styles.iconContainer}>
            <Ionicons name="shield-checkmark" size={48} color="#6B7C5A" />
          </Animated.View>
          <Text style={styles.title}>App Permissions</Text>
          <Text style={styles.subtitle}>
            Grant permissions to unlock all features and get the best experience
          </Text>
        </Animated.View>

        {/* Permissions List */}
        <View style={styles.permissionsList}>
          {permissions.map((permission, index) => (
            <Animated.View
              key={permission.id}
              entering={SlideInLeft.delay(600 + index * 150).duration(600)}
              style={styles.permissionCard}
            >
              <View style={styles.permissionContent}>
                <View style={[styles.permissionIcon, { backgroundColor: `${permission.color}15` }]}>
                  <Ionicons name={permission.icon} size={24} color={permission.color} />
                </View>

                <View style={styles.permissionText}>
                  <Text style={styles.permissionTitle}>{permission.title}</Text>
                  <Text style={styles.permissionDescription}>{permission.description}</Text>
                </View>

                <TouchableOpacity
                  style={[
                    styles.permissionButton,
                    permission.granted && styles.permissionButtonGranted
                  ]}
                  onPress={() => !permission.granted && requestPermission(permission)}
                  disabled={permission.granted}
                >
                  <Ionicons
                    name={permission.granted ? 'checkmark' : 'add'}
                    size={20}
                    color={permission.granted ? '#FFFFFF' : '#6B7C5A'}
                  />
                </TouchableOpacity>
              </View>
            </Animated.View>
          ))}
        </View>

        {/* Continue Button */}
        <Animated.View entering={FadeInUp.delay(1200).duration(600)} style={styles.continueContainer}>
          <TouchableOpacity
            style={[styles.continueButton, allGranted && styles.continueButtonEnabled]}
            onPress={handleContinue}
          >
            <Text style={[styles.continueText, allGranted && styles.continueTextEnabled]}>
              {allGranted ? 'Continue to App' : 'Continue Anyway'}
            </Text>
            <Ionicons
              name="arrow-forward"
              size={20}
              color={allGranted ? '#FFFFFF' : '#6B7C5A'}
            />
          </TouchableOpacity>

          {!allGranted && (
            <Text style={styles.skipText}>
              You can grant permissions later in Settings
            </Text>
          )}
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: 80,
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: '800',
    color: '#6B7C5A',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    lineHeight: 24,
  },
  permissionsList: {
    gap: 16,
    marginBottom: 32,
  },
  permissionCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  permissionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  permissionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  permissionText: {
    flex: 1,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    lineHeight: 20,
  },
  permissionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#6B7C5A',
  },
  permissionButtonGranted: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  continueContainer: {
    paddingTop: 24,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 32,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderWidth: 2,
    borderColor: '#6B7C5A',
    gap: 12,
  },
  continueButtonEnabled: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  continueText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B7C5A',
    letterSpacing: 0.5,
  },
  continueTextEnabled: {
    color: '#FFFFFF',
  },
  skipText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    marginTop: 16,
  },
});

export default PermissionsScreen;

{"expo": {"name": "Nutri AI", "slug": "nutri-ai-mobile", "owner": "saim3333", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "platforms": ["ios", "android"], "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#6B7C5A"}, "ios": {"supportsTablet": true, "infoPlist": {"NSMotionUsageDescription": "This app uses motion sensors to track your steps and activity for health monitoring.", "NSHealthShareUsageDescription": "This app needs access to health data to provide personalized nutrition recommendations.", "NSHealthUpdateUsageDescription": "This app needs to update health data to track your nutrition progress.", "NSMicrophoneUsageDescription": "This app needs access to the microphone for voice-to-text input in Ask AI and Recipe search features.", "NSSpeechRecognitionUsageDescription": "This app uses speech recognition to convert your voice to text for easier input.", "NSCameraUsageDescription": "This app needs access to the camera to scan food items and analyze meals for nutrition tracking.", "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to select images for meal analysis.", "NSLocationWhenInUseUsageDescription": "This app needs location access to find nearby restaurants and provide location-based nutrition recommendations."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "icon": "./assets/icon.png", "permissions": ["android.permission.ACTIVITY_RECOGNITION", "android.permission.BODY_SENSORS", "android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.INTERNET", "android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION"], "package": "com.saim3333.nutriai"}, "plugins": ["expo-dev-client", "expo-av", "expo-location", "expo-image-picker"], "runtimeVersion": {"policy": "appVersion"}, "extra": {"eas": {"projectId": "48d20b62-cbd7-4337-bbcc-937420ff7b53"}}}}